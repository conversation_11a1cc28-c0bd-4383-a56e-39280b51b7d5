package com.huiver.wen.base.net.url

import com.huiver.wen.base.net.parser.RetrofitUrlManager
import okhttp3.HttpUrl



interface UrlParser {
    /**
     * 这里可以做一些初始化操作
     *
     * @param retrofitUrlManager [RetrofitUrlManager]
     */
    fun init(retrofitUrlManager: RetrofitUrlManager)

    /**
     * 将 [RetrofitUrlManager.mDomainNameHub] 中映射的 URL 解析成完整的[HttpUrl]
     * 用来替换 @[Request.url] 达到动态切换 URL
     *
     * @param domainUrl 用于替换的 URL 地址
     * @param url       旧 URL 地址
     * @return
     */
    fun parseUrl(domainUrl: HttpUrl?, url: HttpUrl?): HttpUrl?
}