package com.huiver.wen.base.net

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

abstract class BaseRepository<T : Any>(private val serviceClass: Class<T>, private val baseUrl: String) {


    protected fun <R> safeApiCall(apiCall: suspend () -> R): Flow<NetworkResult<R>> = flow {
        emit(NetworkResult.Loading)
        try {
            emit(NetworkResult.Success(apiCall()))
        } catch (e: Exception) {
            emit(ExceptionHandler.handle(e))
        }
    }
}