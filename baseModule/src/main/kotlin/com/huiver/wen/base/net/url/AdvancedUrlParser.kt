package com.huiver.wen.base.net.url

import android.text.TextUtils
import android.util.LruCache
import com.huiver.wen.base.net.parser.RetrofitUrlManager
import okhttp3.HttpUrl


class AdvancedUrlParser :UrlParser  {
    private lateinit var mRetrofitUrlManager: RetrofitUrlManager
    private lateinit var mCache: LruCache<String, String>
    override fun init(retrofitUrlManager: RetrofitUrlManager) {
        mCache = LruCache(100)
        this.mRetrofitUrlManager = retrofitUrlManager
    }

    override fun parseUrl(domainUrl: HttpUrl?, url: HttpUrl?): HttpUrl? {
        if (null == domainUrl) return url
        val urlKey = getKey(domainUrl,url)
        val urlValue = mCache[urlKey]
        val urlSize = url?.pathSize?:0
        val builder = url?.newBuilder()?:HttpUrl.Builder()
        if (TextUtils.isEmpty(urlValue)){
            for (i in 0..<urlSize) {
                //当删除了上一个 index, PathSegment 的 item 会自动前进一位, 所以 remove(0) 就好
                builder.removePathSegment(0)
            }

            val newPathSegments: MutableList<String> = ArrayList()
            newPathSegments.addAll(domainUrl.encodedPathSegments)
            if (urlSize > mRetrofitUrlManager.pathSize) {
                val encodedPathSegments = url?.encodedPathSegments?: arrayListOf()
                for (i in mRetrofitUrlManager.pathSize..<encodedPathSegments.size) {
                    newPathSegments.add(encodedPathSegments[i])
                }
            } else if (urlSize < mRetrofitUrlManager.pathSize){


                val message = if (url != null){
                    val finalPath=  "${url.scheme}://${url.host}${url.encodedPath}"
                    "Your final path is $finalPath, but the baseUrl of your RetrofitUrlManager#startAdvancedModel is ${ mRetrofitUrlManager.baseUrl?.scheme?:""}://${mRetrofitUrlManager.baseUrl?.scheme?:""}${mRetrofitUrlManager.baseUrl?.encodedPath?:""}"

                }else{
                    ""
                }
                throw IllegalArgumentException(message)
            }

            for (pathSegment in newPathSegments) {
                builder.addEncodedPathSegment(pathSegment)
            }

        }else{
            builder.encodedPath(urlValue)
        }

        val httpUrl = builder
            .scheme(domainUrl.scheme)
            .host(domainUrl.host)
            .port(domainUrl.port)
            .build()

        if (TextUtils.isEmpty(urlValue)) {
            mCache.put(urlKey, httpUrl.encodedPath)
        }
        return httpUrl
    }


    private fun getKey(domainUrl: HttpUrl?, url: HttpUrl?): String {
        return "${domainUrl?.encodedPath?:""}${url?.encodedPath?:""}${mRetrofitUrlManager.pathSize}"

    }
}