package com.huiver.wen.base.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.huiver.wen.base.module.R
import com.huiver.wen.base.module.databinding.BaseEmptyViewBinding


class CommonEmptyView @JvmOverloads constructor(context: Context, attr: AttributeSet? = null, defStyleAttr: Int = 0) : LinearLayout (
    context,
    attr,
    defStyleAttr
){

    private val mBinding by lazy {
        BaseEmptyViewBinding.bind(this)
    }

    init {
        inflate(context, R.layout.base_empty_view, this)
    }

    fun getEmptyImageView(): ImageView {
        return mBinding.ivEmpty

    }

    fun getEmptyContentTextView(): TextView {
        return mBinding.tvContent
    }

    fun getReloadView() = mBinding.tvReload



}