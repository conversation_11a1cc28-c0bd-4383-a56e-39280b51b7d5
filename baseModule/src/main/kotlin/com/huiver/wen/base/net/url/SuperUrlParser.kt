package com.huiver.wen.base.net.url

import android.text.TextUtils
import androidx.collection.LruCache
import com.huiver.wen.base.net.parser.RetrofitUrlManager
import com.huiver.wen.base.net.parser.RetrofitUrlManager.IDENTIFICATION_PATH_SIZE
import okhttp3.HttpUrl


/**
 * ================================================
 * 超级解析器
 * 超级模式属于高级模式的加强版, 优先级高于高级模式, 在高级模式中, 需要传入一个 BaseUrl (您传入 Retrofit 的 BaseUrl) 作为被替换的基准
 * 如这个传入的 BaseUrl 为 "https://www.github.com/wiki/part" (PathSize = 2), 那框架会将所有需要被替换的 Url 中的 域名 以及 域名 后面的前两个 pathSegments
 * 使用您传入 {@link RetrofitUrlManager#putDomain(String, String)} 方法的 Url 替换掉
 * 但如果突然有一小部分的 Url 只想将 "https://www.github.com/wiki" (PathSize = 1) 替换掉, 后面的 pathSegment '/part' 想被保留下来
 * 这时项目中就出现了多个 PathSize 不同的需要被替换的 BaseUrl
 * <p>
 * 使用高级模式实现这种需求略显麻烦, 所以我创建了超级模式, 让每一个 Url 都可以随意指定不同的 BaseUrl (PathSize 自己定) 作为被替换的基准
 * 使 RetrofitUrlManager 可以从容应对各种复杂的需求
 * <p>
 * 超级模式也需要手动开启, 但与高级模式不同的是, 开启超级模式并不需要调用 API, 只需要在 Url 中加入 {@link RetrofitUrlManager#IDENTIFICATION_PATH_SIZE} + PathSize
 * <p>
 * 替换规则如下:
 * 1.
 * 旧 URL 地址为 https://www.github.com/wiki/part#baseurl_path_size=1, #baseurl_path_size=1 表示其中 BaseUrl 为 https://www.github.com/wiki
 * 您调用 {@link RetrofitUrlManager#putDomain(String, String)}方法传入的 URL 地址是 https://www.google.com/api
 * 经过本解析器解析后生成的新 URL 地址为 http://www.google.com/api/part
 * <p>
 * 2.
 * 旧 URL 地址为 https://www.github.com/wiki/part#baseurl_path_size=1, #baseurl_path_size=1 表示其中 BaseUrl 为 https://www.github.com/wiki
 * 您调用 {@link RetrofitUrlManager#putDomain(String, String)}方法传入的 URL 地址是 https://www.google.com
 * 经过本解析器解析后生成的新 URL 地址为 http://www.google.com/part
 * <p>
 * 3.
 * 旧 URL 地址为 https://www.github.com/wiki/part#baseurl_path_size=0, #baseurl_path_size=0 表示其中 BaseUrl 为 https://www.github.com
 * 您调用 {@link RetrofitUrlManager#putDomain(String, String)}方法传入的 URL 地址是 https://www.google.com/api
 * 经过本解析器解析后生成的新 URL 地址为 http://www.google.com/api/wiki/part
 * <p>
 * 4.
 * 旧 URL 地址为 https://www.github.com/wiki/part/issues/1#baseurl_path_size=3, #baseurl_path_size=3 表示其中 BaseUrl 为 https://www.github.com/wiki/part/issues
 * 您调用 {@link RetrofitUrlManager#putDomain(String, String)}方法传入的 URL 地址是 https://www.google.com/api
 * 经过本解析器解析后生成的新 URL 地址为 http://www.google.com/api/1
 *
 * @see UrlParser
 */
class SuperUrlParser : UrlParser {
    private var mRetrofitUrlManager: RetrofitUrlManager? = null
    private var mCache: LruCache<String, String>? = null
    override fun init(retrofitUrlManager: RetrofitUrlManager) {

        this.mRetrofitUrlManager = retrofitUrlManager
        this.mCache = LruCache(100)
    }

    override fun parseUrl(domainUrl: HttpUrl?, url: HttpUrl?): HttpUrl? {
        if (null == domainUrl) return url
        val builder = url?.newBuilder()
        val pathSize: Int = resolvePathSize(url, builder)
        if (TextUtils.isEmpty(mCache?.get(getKey(domainUrl,url,pathSize)))){
            val urlPathSize = url?.pathSize?:0
            if (url!=null){
                for (i in 0..<urlPathSize) {
                    //当删除了上一个 index, PathSegment 的 item 会自动前进一位, 所以 remove(0) 就好
                    builder?.removePathSegment(0)
                }
            }
            val newPathSegments = arrayListOf<String>()
            domainUrl.let {
                newPathSegments.addAll(it.encodedPathSegments);
            }
            if (urlPathSize > pathSize) {
                val encodedPathSegments = url?.encodedPathSegments?: arrayListOf()
                for (i in pathSize..<encodedPathSegments.size) {
                    newPathSegments.add(encodedPathSegments[i])
                }
            } else if  (urlPathSize < pathSize){

                val message = if (url != null){
                    val finalPath=  "${url.scheme}://${url.host}${url.encodedPath}"
                    "Your final path is $finalPath, the pathSize = ${url.pathSize}, but the #baseurl_path_size = $pathSize, #baseurl_path_size must be less than or equal to pathSize of the final path"

                }else{
                    ""
                }
                throw  IllegalArgumentException(message)


            }
            for (pathSegment in newPathSegments) {
                builder!!.addEncodedPathSegment(pathSegment)
            }
        }else{
            val value = mCache?.get(getKey(domainUrl, url, pathSize))?:""
            builder?.encodedPath(value)
        }

        val httpUrl = builder?.scheme(domainUrl.scheme)
            ?.host(domainUrl.host)
            ?.port(domainUrl.port)
            ?.build()
        val key =  getKey(domainUrl, url, pathSize)
        val value = mCache?.get(key)
        if (TextUtils.isEmpty(value)) {
            mCache?.put(key, httpUrl?.encodedPath?:"")
        }
        return httpUrl
    }


    private fun resolvePathSize(httpUrl: HttpUrl?, builder: HttpUrl.Builder?): Int {


        if (TextUtils.isEmpty(httpUrl?.fragment)) {
            return 0
        }
        val fragment = httpUrl?.fragment!!
        var pathSize = 0
        val newFragment = StringBuffer()

        if (fragment.indexOf("#") == -1) {
            val split =
                fragment.split("=".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            if (split.size > 1) {
                pathSize = split[1].toInt()
            }
        } else {
            if (fragment.indexOf(IDENTIFICATION_PATH_SIZE) == -1) {
                val index = fragment.indexOf("#")
                newFragment.append(fragment.substring(index + 1, fragment.length))
                val split = fragment.substring(0, index).split("=".toRegex())
                    .dropLastWhile { it.isEmpty() }.toTypedArray()
                if (split.size > 1) {
                    pathSize = split[1].toInt()
                }
            } else {
                val split: Array<String> = fragment.split(IDENTIFICATION_PATH_SIZE.toRegex())
                    .dropLastWhile { it.isEmpty() }.toTypedArray()
                newFragment.append(split[0])
                if (split.size > 1) {
                    val index = split[1].indexOf("#")
                    if (index != -1) {
                        newFragment.append(split[1].substring(index, split[1].length))
                        val substring = split[1].substring(0, index)
                        if (!TextUtils.isEmpty(substring)) {
                            pathSize = substring.toInt()
                        }
                    } else {
                        pathSize = split[1].toInt()
                    }
                }
            }
        }
        if (TextUtils.isEmpty(newFragment.toString())) {
            builder?.fragment(null)
        } else {
            builder?.fragment(newFragment.toString())
        }
        return pathSize
    }

    private fun getKey(domainUrl: HttpUrl?, url: HttpUrl?, pathSize: Int): String {
        return "${domainUrl?.encodedPath?:""}${url?.encodedPath?:""}${pathSize}"


    }
}