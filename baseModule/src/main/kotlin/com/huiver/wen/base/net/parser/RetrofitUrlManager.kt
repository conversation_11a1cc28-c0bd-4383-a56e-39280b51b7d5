/*
 * Copyright 2017 JessYan
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.huiver.wen.base.net.parser

import android.text.TextUtils
import com.huiver.wen.base.net.url.DefaultUrlParser
import com.huiver.wen.base.net.url.UrlParser
import com.huiver.wen.lib.common.LogUtil
import okhttp3.HttpUrl
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Request


object RetrofitUrlManager {
    private val mDomainNameHub = HashMap<String, HttpUrl>()
    private val mListeners = ArrayList<com.huiver.wen.base.net.parser.OnUrlChangeListener>()
    private var mUrlParser: UrlParser = DefaultUrlParser().apply { init(this@RetrofitUrlManager) }
    var baseUrl: HttpUrl? = null
        private set
    var pathSize = 0
        private set
    private var isRun = true // Default is running, can be stopped at any time


    private val mInterceptor = Interceptor { chain ->
        if (!com.huiver.wen.base.net.parser.RetrofitUrlManager.isRun) { // Can stop the framework at runtime by setRun(false)
            chain.proceed(chain.request())
        } else {
            chain.proceed(com.huiver.wen.base.net.parser.RetrofitUrlManager.processRequest(chain.request()))
        }
    }


        private const val TAG = "RetrofitUrlManager"
        private const val DOMAIN_NAME = "Domain-Name"
        const val GLOBAL_DOMAIN_NAME = "me.jessyan.retrofiturlmanager.globalDomainName"
        const val DOMAIN_NAME_HEADER = "${com.huiver.wen.base.net.parser.RetrofitUrlManager.DOMAIN_NAME}: "

        /**
         * 如果在 Url 地址中加入此标识符, 框架将不会对此 Url 进行任何切换 BaseUrl 的操作
         */
        const val IDENTIFICATION_IGNORE = "#url_ignore"

        /**
         * 如果在 Url 地址中加入此标识符, 意味着您想对此 Url 开启超级模式,
         * 框架会将 '=' 后面的数字作为 PathSize, 来确认最终需要被超级模式替换的 BaseUrl
         */
        const val IDENTIFICATION_PATH_SIZE = "#baseurl_path_size="

        private val DEPENDENCY_OKHTTP:Boolean by lazy {
            try {
                Class.forName("okhttp3.OkHttpClient")
                true
            } catch (e: ClassNotFoundException) {
                false
            }
        }



    init {
        check(!com.huiver.wen.base.net.parser.RetrofitUrlManager.DEPENDENCY_OKHTTP){
            throw IllegalStateException("Must be dependency Okhttp")
        }
        com.huiver.wen.base.net.parser.RetrofitUrlManager.setUrlParser(com.huiver.wen.base.net.parser.RetrofitUrlManager.mUrlParser)
    }

    /**
     * Pass in [OkHttpClient.Builder] to configure some parameters required by this framework
     */
    fun with(builder: OkHttpClient.Builder): OkHttpClient.Builder {
       return builder.addInterceptor(com.huiver.wen.base.net.parser.RetrofitUrlManager.mInterceptor)
    }

    private fun processRequest(request: Request): Request {
        val newBuilder = request.newBuilder()
        val url = request.url.toString()

        //如果 Url 地址中包含 IDENTIFICATION_IGNORE 标识符, 框架将不会对此 Url 进行任何切换 BaseUrl 的操作
        if (url.contains(com.huiver.wen.base.net.parser.RetrofitUrlManager.IDENTIFICATION_IGNORE)) {
            return com.huiver.wen.base.net.parser.RetrofitUrlManager.pruneIdentification(
                newBuilder,
                url
            )
        }

        val domainName =
            com.huiver.wen.base.net.parser.RetrofitUrlManager.obtainDomainNameFromHeaders(request)
        val listeners = com.huiver.wen.base.net.parser.RetrofitUrlManager.listenersToArray()

        val httpUrl = if (!TextUtils.isEmpty(domainName)) {
            com.huiver.wen.base.net.parser.RetrofitUrlManager.notifyListener(
                request,
                domainName!!,
                listeners
            )
            com.huiver.wen.base.net.parser.RetrofitUrlManager.fetchDomain(domainName)
        } else {
            com.huiver.wen.base.net.parser.RetrofitUrlManager.notifyListener(
                request,
                com.huiver.wen.base.net.parser.RetrofitUrlManager.GLOBAL_DOMAIN_NAME,
                listeners
            )
            com.huiver.wen.base.net.parser.RetrofitUrlManager.getGlobalDomain()
        }

        if (httpUrl != null) {
            val newUrl = com.huiver.wen.base.net.parser.RetrofitUrlManager.mUrlParser.parseUrl(httpUrl, request.url)

            LogUtil.d("The new url is { $newUrl }, old url is { ${request.url} }")
            listeners?.forEach { listener ->
                (listener as com.huiver.wen.base.net.parser.OnUrlChangeListener).onUrlChanged(newUrl, request.url)
            }

            return newBuilder.url(newUrl!!).build()
        }

       return newBuilder.build()



    }

    private fun pruneIdentification(newBuilder: Request.Builder, url: String): Request {
        val buffer = StringBuffer()
        url.split(com.huiver.wen.base.net.parser.RetrofitUrlManager.IDENTIFICATION_IGNORE).forEach { buffer.append(it) }
        return newBuilder.url(buffer.toString()).build()
    }

    private fun notifyListener(request: Request, domainName: String, listeners: Array<Any>?) {
        listeners?.forEach { listener ->
            (listener as com.huiver.wen.base.net.parser.OnUrlChangeListener).onUrlChangeBefore(request.url, domainName)
        }
    }





    fun startAdvancedModel(baseUrl: String) {
        val httpUrl = baseUrl.toHttpUrlOrNull()
        checkNotNull(httpUrl){"url format error"}.apply {
            com.huiver.wen.base.net.parser.RetrofitUrlManager.startAdvancedModel(this)
        }

    }

    @Synchronized
    fun startAdvancedModel(baseUrl: HttpUrl) {

        com.huiver.wen.base.net.parser.RetrofitUrlManager.baseUrl = baseUrl
        com.huiver.wen.base.net.parser.RetrofitUrlManager.pathSize = baseUrl.pathSize
        val baseUrlPathSegments = baseUrl.pathSegments
        if (baseUrlPathSegments.isNotEmpty() && baseUrlPathSegments.last() == "") {
            com.huiver.wen.base.net.parser.RetrofitUrlManager.pathSize -= 1
        }
    }



    fun isAdvancedModel(): Boolean = com.huiver.wen.base.net.parser.RetrofitUrlManager.baseUrl != null

    fun setUrlNotChange(url: String): String {

        return url + com.huiver.wen.base.net.parser.RetrofitUrlManager.IDENTIFICATION_IGNORE
    }

    fun setPathSizeOfUrl(url: String, pathSize: Int): String {

        require(pathSize >= 0) { "pathSize must be >= 0" }
        return url + com.huiver.wen.base.net.parser.RetrofitUrlManager.IDENTIFICATION_PATH_SIZE + pathSize
    }

    fun setGlobalDomain(globalDomain: String) {
        synchronized(com.huiver.wen.base.net.parser.RetrofitUrlManager.mDomainNameHub) {
            checkNotNull(globalDomain.toHttpUrlOrNull()).apply {
                com.huiver.wen.base.net.parser.RetrofitUrlManager.mDomainNameHub[com.huiver.wen.base.net.parser.RetrofitUrlManager.GLOBAL_DOMAIN_NAME] = this
            }

        }
    }

    @Synchronized
    fun getGlobalDomain(): HttpUrl? = com.huiver.wen.base.net.parser.RetrofitUrlManager.mDomainNameHub[com.huiver.wen.base.net.parser.RetrofitUrlManager.GLOBAL_DOMAIN_NAME]

    fun removeGlobalDomain() {
        synchronized(com.huiver.wen.base.net.parser.RetrofitUrlManager.mDomainNameHub) {
            com.huiver.wen.base.net.parser.RetrofitUrlManager.mDomainNameHub.remove(com.huiver.wen.base.net.parser.RetrofitUrlManager.GLOBAL_DOMAIN_NAME)
        }
    }

    fun putDomain(domainName: String, domainUrl: String) {

        synchronized(com.huiver.wen.base.net.parser.RetrofitUrlManager.mDomainNameHub) {
            checkNotNull(domainUrl.toHttpUrlOrNull()).apply {
                com.huiver.wen.base.net.parser.RetrofitUrlManager.mDomainNameHub[domainName] = this
            }
        }
    }

    @Synchronized
    fun fetchDomain(domainName: String): HttpUrl? {
        return com.huiver.wen.base.net.parser.RetrofitUrlManager.mDomainNameHub[domainName]
    }

    fun removeDomain(domainName: String) {
        synchronized(com.huiver.wen.base.net.parser.RetrofitUrlManager.mDomainNameHub) {
            com.huiver.wen.base.net.parser.RetrofitUrlManager.mDomainNameHub.remove(domainName)
        }
    }

    fun clearAllDomain() {
        com.huiver.wen.base.net.parser.RetrofitUrlManager.mDomainNameHub.clear()
    }

    @Synchronized
    fun haveDomain(domainName: String): Boolean = com.huiver.wen.base.net.parser.RetrofitUrlManager.mDomainNameHub.containsKey(domainName)

    @Synchronized
    fun domainSize(): Int = com.huiver.wen.base.net.parser.RetrofitUrlManager.mDomainNameHub.size

    fun setUrlParser(parser: UrlParser) {
        com.huiver.wen.base.net.parser.RetrofitUrlManager.mUrlParser = parser
    }

    fun registerUrlChangeListener(listener: com.huiver.wen.base.net.parser.OnUrlChangeListener) {
        synchronized(com.huiver.wen.base.net.parser.RetrofitUrlManager.mListeners) {
            com.huiver.wen.base.net.parser.RetrofitUrlManager.mListeners.add(listener)
        }
    }

    fun unregisterUrlChangeListener(listener: com.huiver.wen.base.net.parser.OnUrlChangeListener) {

        synchronized(com.huiver.wen.base.net.parser.RetrofitUrlManager.mListeners) {
            com.huiver.wen.base.net.parser.RetrofitUrlManager.mListeners.remove(listener)
        }
    }

    private fun listenersToArray(): Array<Any>? {
        synchronized(com.huiver.wen.base.net.parser.RetrofitUrlManager.mListeners) {
            return if (com.huiver.wen.base.net.parser.RetrofitUrlManager.mListeners.isNotEmpty()) com.huiver.wen.base.net.parser.RetrofitUrlManager.mListeners.toArray() else null
        }
    }

    private fun obtainDomainNameFromHeaders(request: Request): String? {
        val headers = request.headers(com.huiver.wen.base.net.parser.RetrofitUrlManager.DOMAIN_NAME)
        if (headers.isEmpty()) return null
        require(headers.size==1){
            "Only one Domain-Name in the headers"
        }

        return request.header(com.huiver.wen.base.net.parser.RetrofitUrlManager.DOMAIN_NAME)
    }


}

