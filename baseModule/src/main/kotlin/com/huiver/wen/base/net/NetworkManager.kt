package com.huiver.wen.base.net

import com.huiver.wen.base.net.parser.RetrofitUrlManager
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit


object NetworkManager {
    private val DEFAULT_TIME_OUT: Long = 30

    private val mOkHttpClient: OkHttpClient by lazy {
        RetrofitUrlManager.with(OkHttpClient.Builder())
            .readTimeout(DEFAULT_TIME_OUT, TimeUnit.SECONDS)
            .connectTimeout(DEFAULT_TIME_OUT, TimeUnit.SECONDS)
            .build()
    }
    private val mRetrofit: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl(Api.APP_DEFAULT_DOMAIN)
            .client(mOkHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    fun <T> createService(service: Class<T>): T {
        return mRetrofit.create(service)
    }
}