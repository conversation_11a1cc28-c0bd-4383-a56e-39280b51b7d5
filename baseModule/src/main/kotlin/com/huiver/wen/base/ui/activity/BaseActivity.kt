package com.huiver.wen.base.ui.activity

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.viewbinding.ViewBinding
import com.huiver.wen.base.ui.IBaseBinding
import com.huiver.wen.base.ui.IBaseProvider
import dagger.hilt.android.AndroidEntryPoint


abstract class BaseActivity<VB:ViewBinding> :AbstractStatusBarActivity(),IBaseProvider,IBaseBinding<VB>{

    protected val mBinding: VB by lazy { getViewBinding(layoutInflater) }
    private companion object {
        const val ACTION_ACTIVITY_FINISH = "action_activity_finish"
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (hasRegisterFinishReceiver()) {
            registerActivityFinishReceiver()
        }


        initData()
        setContentView(mBinding.root)
        initView()
        onObserver()
        onRequest()

    }


    /**
     * 是否注册Finish广播
     * */
    protected open fun hasRegisterFinishReceiver() = true


    private val mBroadcastReceiver by lazy {
        FinishBroadcastReceiver()
    }


    override fun onDestroy() {
        super.onDestroy()
        if (hasRegisterFinishReceiver()) {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(mBroadcastReceiver)
        }
    }


    /**
     * 发送Activity 释放广播
     */


    fun sendActivityFinishBroadcast() {
        val intent = Intent()
        intent.action = ACTION_ACTIVITY_FINISH
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
    }
    private fun registerActivityFinishReceiver() {
        val filter = IntentFilter()
        filter.addAction(ACTION_ACTIVITY_FINISH)
        LocalBroadcastManager.getInstance(this).registerReceiver(mBroadcastReceiver, filter)
    }

    //Activity 释放广播
    private inner class FinishBroadcastReceiver : BroadcastReceiver() {

        override fun onReceive(context: Context, intent: Intent) {

            when (intent.action) {
                ACTION_ACTIVITY_FINISH -> {
                    finish()
                }
            }

        }
    }
}