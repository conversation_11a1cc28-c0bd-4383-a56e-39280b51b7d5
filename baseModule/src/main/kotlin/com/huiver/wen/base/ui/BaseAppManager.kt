package com.huiver.wen.base.ui

import androidx.appcompat.app.AppCompatActivity
import java.util.Stack

class BaseAppManager private constructor() {
    private val activityStack = Stack<AppCompatActivity>()
    companion object {
        //双重校验锁式
        val instance: BaseAppManager by lazy {
            BaseAppManager()
        }
    }

    fun addActivity(activity: AppCompatActivity) {
        activityStack.add(activity)

    }
    fun removeActivity(activity: AppCompatActivity){
        activityStack.remove(activity)

    }

    /**
     * 是否有activity
     */
    fun isActivity(): Boolean {
        return activityStack.isEmpty()
    }
    /**
     * 获取当前Activity（堆栈中最后一个压入的）
     */
    fun currentActivity(): AppCompatActivity {
        return activityStack.lastElement()
    }

    /**
     * 结束当前Activity（堆栈中最后一个压入的）
     */
    fun finishCurrentActivity() {
        val activity = activityStack.lastElement()
        finishActivity(activity)
    }

    /**
     * 结束指定的Activity
     */
    fun finishActivity(activity: AppCompatActivity) {
        if (activity.isFinishing) {
            activity.finish()
        }
    }

    /**
     * 结束指定类名的Activity
     */
    fun finishActivity(cls: Class<*>) {
        for (activity in activityStack) {
            if (activity.javaClass == cls) {
                finishActivity(activity)
                break
            }
        }
    }

    /**
     * 结束所有Activity
     */
    fun finishAllActivity() {
        var i = 0
        val size: Int = activityStack.size
        while (i < size) {
            if (null != activityStack[i]) {
                finishActivity(activityStack[i])
            }
            i++
        }
        activityStack.clear()
    }



}