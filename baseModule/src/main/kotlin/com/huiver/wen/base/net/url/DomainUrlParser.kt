package com.huiver.wen.base.net.url

import android.text.TextUtils
import android.util.LruCache
import com.huiver.wen.base.net.parser.RetrofitUrlManager
import okhttp3.HttpUrl


/**
 *  * ================================================
 *  * 域名解析器, 此解析器用来解析域名, 默认将您的域名作为 BaseUrl, 只会将旧 URL 地址中的域名替换成你想要的地址
 *  * <p>
 *  * 比如:
 *  * 1.
 *  * 旧 URL 地址为 https://www.github.com/wiki, 您调用 {@link RetrofitUrlManager#putDomain(String, String)}
 *  * 方法传入的 URL 地址是 https://www.google.com/api, 经过本解析器解析后生成的新 URL 地址为 http://www.google.com/api/wiki
 *  * <p>
 *  * 2.
 *  * 旧 URL 地址为 https://www.github.com/wiki, 您调用 {@link RetrofitUrlManager#putDomain(String, String)}
 *  * 方法传入的 URL 地址是 https://www.google.com, 经过本解析器解析后生成的新 URL 地址为 http://www.google.com/wiki
 *  * ================================================
 */
class DomainUrlParser :UrlParser {
    private lateinit var mCache: LruCache<String, String>
    override fun init(retrofitUrlManager: RetrofitUrlManager) {
        this.mCache = LruCache(100)
    }

    override fun parseUrl(domainUrl: HttpUrl?, url: HttpUrl?): HttpUrl? {
        if (null == domainUrl) return url

        val builder = url?.newBuilder()?: HttpUrl.Builder()
        val key = getKey(domainUrl, url)
        val value = mCache[key]
        val urlPathSize = url?.pathSize?:0


        if (TextUtils.isEmpty(value)) {
            for (i in 0..< urlPathSize) {
                //当删除了上一个 index, PathSegment 的 item 会自动前进一位, 所以 remove(0) 就好
                builder.removePathSegment(0)
            }

            val newPathSegments: MutableList<String> = ArrayList()
            newPathSegments.addAll(domainUrl.encodedPathSegments)
            url?.let {
                newPathSegments.addAll(it.encodedPathSegments)
            }


            for (pathSegment in newPathSegments) {
                builder.addEncodedPathSegment(pathSegment)
            }
        } else {
            builder.encodedPath(value)
        }

        val httpUrl = builder
            .scheme(domainUrl.scheme)
            .host(domainUrl.host)
            .port(domainUrl.port)
            .build()

        if (TextUtils.isEmpty(value)) {
            mCache.put(value, httpUrl.encodedPath)
        }
        return httpUrl
    }

    private fun getKey(domainUrl: HttpUrl?, url: HttpUrl?): String {
        return "${ domainUrl?.encodedPath?:""}${url?.encodedPath?:""}"
    }
}