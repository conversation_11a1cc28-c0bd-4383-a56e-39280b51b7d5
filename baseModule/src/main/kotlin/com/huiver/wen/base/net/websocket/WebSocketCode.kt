package com.huiver.wen.base.net.websocket

enum class WebSocketCode(val code: Int, val message: String) {
    CONNECT_SUCCESS(0, "websocket 连接成功"),
    HEARTBEAT(1, "心跳包"),
    TOKEN_EXPIRED(102, "token失效"),
    TG_VERIFICATION_CODE(101, "TG手机号登录验证码通知客户端");

    companion object {
        fun getWebSocketCodeByCode(code: Int): WebSocketCode? {
            for (wsCode in entries) {
                if (wsCode.code == code) {
                    return wsCode
                }
            }
            return null // 如果没有匹配的 code，返回 null
        }
    }
}
