package com.huiver.wen.base.ui.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.viewbinding.ViewBinding
import com.huiver.wen.base.ui.IBaseBinding
import com.huiver.wen.base.ui.IBaseProvider
import com.huiver.wen.lib.common.LogUtil


abstract class BaseFragment <VB: ViewBinding>:Fragment() ,IBaseProvider ,IBaseBinding<VB>{

    private var _binding: VB?= null
    protected val mBinding get() = requireNotNull(_binding)


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d("fragment onCreate")
        initData()

    }

    override fun onStart() {
        super.onStart()

        LogUtil.d("fragment onStart")
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        LogUtil.d("fragment onCreateView")
        _binding = getViewBinding(inflater)

        return mBinding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        LogUtil.d("fragment onViewCreated")
        initView()
        onObserver()
        onRequest()

    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        LogUtil.d("fragment onDestroyView")

    }

    override fun onDestroy() {
        super.onDestroy()
        LogUtil.d("fragment onDestroy")
    }


    override fun onDetach() {

        super.onDetach()
        LogUtil.d("fragment onDetach")
    }
}