package com.huiver.wen.base.common.data

import com.huiver.wen.base.common.data.BasePage.Companion.DEFAULT_LIMIT
import com.huiver.wen.base.common.data.BasePage.Companion.DEFAULT_PAGE_STARR

data class PageSizeData( private var currentPage: Int = 0, var limit: Int = 10, private var isMore: Boolean = true) {
    fun next(): Int {
        return currentPage + 1
    }

    fun addCurrentPage() {
        currentPage += 1
    }

    fun resetPage(){
        currentPage = DEFAULT_PAGE_STARR
        limit = DEFAULT_LIMIT
    }
    fun getCurrentPage() = currentPage

    fun setIsMore(size:Int =0){
        if (size>0) addCurrentPage()
        isMore =  (size >= limit)
    }

    fun reset(){
        currentPage = 0
        isMore = true
    }

    fun getIsMore() = isMore

    fun isFirstPage()= currentPage ==DEFAULT_PAGE_STARR
}