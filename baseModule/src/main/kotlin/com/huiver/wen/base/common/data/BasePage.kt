package com.huiver.wen.base.common.data

import com.google.android.gms.common.util.CollectionUtils
import java.util.Collections
import java.util.Objects

data class BasePage <T> (var currentPage :Int = DEFAULT_PAGE_STARR,val pages:Int= 1,var limit:Int = DEFAULT_LIMIT,val total:Int = 0,val data:MutableList<T>? = null){
    companion object{
        const val DEFAULT_PAGE_STARR = 1
        const val DEFAULT_LIMIT = 10
    }

}