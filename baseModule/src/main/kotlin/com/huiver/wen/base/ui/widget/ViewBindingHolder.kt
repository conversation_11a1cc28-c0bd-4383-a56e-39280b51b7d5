package com.huiver.wen.base.ui.widget

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding


class ViewBindingHolder<VB : ViewBinding> : RecyclerView.ViewHolder {
    val binding: VB

    constructor(binding: VB) : super(binding.root) {
        this.binding = binding
    }

    constructor(
        parent: ViewGroup,
        inflate: (LayoutInflater, ViewGroup, Boolean) -> VB
    ) : this(inflate(LayoutInflater.from(parent.context), parent, false))

}
