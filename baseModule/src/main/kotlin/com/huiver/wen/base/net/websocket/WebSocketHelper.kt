package com.huiver.wen.base.net.websocket

import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import com.huiver.wen.lib.common.LogUtil
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.WebSocket
import okhttp3.WebSocketListener
import org.json.JSONException
import org.json.JSONObject
import java.util.UUID
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.ScheduledThreadPoolExecutor
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger

internal object WebSocketHelper {
    private const val TAG: String = "WebSocketHelper"


    const val HEART_BEAT_DELAY_MILLIS: Int = 10 * 1000 // 心跳间隔(毫秒)
    private const val HEART_BEAT_TIMEOUT_COUNT: Int = 3 // 心跳超时次数
    private const val CLOSE_WEB_SOCKET: Int = 1010 // 关闭socket的状态码
    private const val RECONNECT_DELAY: Int = 3000 // 重连延迟(毫秒)

    var connectionState: com.huiver.wen.base.net.websocket.ConnectionState = com.huiver.wen.base.net.websocket.ConnectionState.DISCONNECTED
        private set

    // 心跳相关
    private var heartbeatExecutor: ScheduledExecutorService

    private var messageListener: WebSocketMessageListener? = null

    private var heartbeatFuture: ScheduledFuture<*>? = null

    private val heartBeatCount = AtomicInteger(0)

    init {


        // 创建单线程定时执行器，用于心跳
        heartbeatExecutor = ScheduledThreadPoolExecutor(1) { r: Runnable? ->
            val thread = Thread(r, "websocket-heartbeat")
            thread.isDaemon = true // 设置为守护线程，不阻止JVM退出
            thread
        }
    }

    private var webSocket: WebSocket? = null
    private var client: OkHttpClient? = null
    /**
     * 设置消息监听器
     * @param listener 监听器实例
     */
    fun setMessageListener(listener: WebSocketMessageListener) {
        this.messageListener = listener
    }
    private var userSeatId: String? = null

    private var token: String? = null
    /**
     * 初始连接WebSocket服务器
     * @param userSeatId 用户座位ID
     * @param token 认证Token
     */
    fun firstConnect(userSeatId: String, token: String) {
        if (TextUtils.isEmpty(userSeatId) || TextUtils.isEmpty(token)) {

            return
        }

        this.userSeatId = userSeatId
        this.token = token


        // 如果已经在连接中，先关闭旧连接
        if (connectionState != com.huiver.wen.base.net.websocket.ConnectionState.DISCONNECTED) {
            close()
        }

        connect()
    }

    /**
     * 建立WebSocket连接
     */
    private fun connect() {
        updateConnectionState(com.huiver.wen.base.net.websocket.ConnectionState.CONNECTING)


        // 使用OkHttp创建WebSocket连接
        if (client == null) {
            client = OkHttpClient.Builder()
                .pingInterval(30, TimeUnit.SECONDS) // 设置ping间隔
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .build()
        }


        val serverHost = "ws://bhc.csbilin.com/tgws/client/{seatId}-{uuid}/SEAT-Android"


        var url = serverHost.replace("{seatId}", userSeatId!!)
        val uuid = UUID.randomUUID().toString().replace("-", "") // 生成一个随机UUID，确保每次连接唯一
        url = url.replace("{uuid}", uuid)


        val request = Request.Builder().url(url).build()
        webSocket = client?.newWebSocket(
            request,
           WebSocketEventListener()
        )
    }

    /**
     * 更新连接状态并通知监听器
     * @param state 新的连接状态
     */
    private fun updateConnectionState(state: com.huiver.wen.base.net.websocket.ConnectionState) {
        if (this.connectionState != state) {
            this.connectionState = state


            if (messageListener != null) {
                messageListener!!.onConnectionStateChanged(state)
            }
        }
    }

    /**
     * 发送握手消息进行认证
     * @param token 认证Token
     */
    private fun sendHandshake(token: String) {
        val message = JSONObject()
        try {
            message.put("type", 0)
            message.put("data", token)
            sendMessage(message.toString())

        } catch (e: JSONException) {
            LogUtil.e("构建握手消息失败: " + e.message)

        }
    }

    /**
     * 发送消息
     * @param message 消息内容
     * @return 是否发送成功
     */
    fun sendMessage(message: String): Boolean {
        if (connectionState == com.huiver.wen.base.net.websocket.ConnectionState.CONNECTED){
            return webSocket?.send(message)?:false
        }

        return false
    }

    /**
     * 启动心跳机制，使用ScheduledExecutorService实现
     */
    private fun startHeartBeat() {
        stopHeartBeat()


        // 重置心跳计数
        heartBeatCount.set(0)


        // 创建心跳任务
        heartbeatFuture = heartbeatExecutor.scheduleWithFixedDelay(
            {
                try {
                    if (connectionState == com.huiver.wen.base.net.websocket.ConnectionState.CONNECTED) {
                        val count = heartBeatCount.incrementAndGet()
                        if (count > HEART_BEAT_TIMEOUT_COUNT) {

                            heartBeatCount.set(0)
                            // 需要切换到主线程进行重连
                            Handler(Looper.getMainLooper()).post { this.reconnect() }
                        } else {
                            sendHeartBeat()

                        }
                    }
                } catch (e: Exception) {
                    LogUtil.e("心跳任务异常: " + e.message)

                }
            },
            HEART_BEAT_DELAY_MILLIS.toLong(),
            HEART_BEAT_DELAY_MILLIS.toLong(),
            TimeUnit.MILLISECONDS
        )
    }

    /**
     * 停止心跳
     */
    private fun stopHeartBeat() {
        if (heartbeatFuture != null && !heartbeatFuture!!.isCancelled) {
            heartbeatFuture?.cancel(true)
            heartbeatFuture = null
        }
        heartBeatCount.set(0)
    }
    /**
     * 关闭WebSocket连接
     */
    fun close() {
        stopHeartBeat()

        if (webSocket != null) {
            webSocket!!.close(CLOSE_WEB_SOCKET, "主动关闭")
            webSocket = null
            LogUtil.d("WebSocket已关闭")
        }

        updateConnectionState(com.huiver.wen.base.net.websocket.ConnectionState.DISCONNECTED)
    }
    /**
     * 重新连接WebSocket
     */
    fun reconnect() {
        if (connectionState != com.huiver.wen.base.net.websocket.ConnectionState.RECONNECTING) {
            updateConnectionState(com.huiver.wen.base.net.websocket.ConnectionState.RECONNECTING)

            LogUtil.d("准备重新连接...")
            if (heartbeatExecutor.isShutdown || heartbeatExecutor.isTerminated) {
                heartbeatExecutor = ScheduledThreadPoolExecutor(1) { r: Runnable? ->
                    val thread = Thread(r, "websocket-heartbeat")
                    thread.isDaemon = true // 设置为守护线程，不阻止JVM退出
                    thread
                }
            }
            if (!heartbeatExecutor.isShutdown && !heartbeatExecutor.isTerminated) {
                // 使用线程池调度延迟重连
                heartbeatExecutor.schedule({
                    Handler(Looper.getMainLooper()).post {
                        close()
                        connect()
                    }
                }, RECONNECT_DELAY.toLong(), TimeUnit.MILLISECONDS)
            }
        }
    }

    /**
     * 发送心跳消息
     */
    private fun sendHeartBeat() {
        val message = JSONObject()
        try {
            message.put("type", 1)
            val state = sendMessage(message.toString())
            LogUtil.e("发送消息: " + state)
        } catch (e: JSONException) {
            LogUtil.e("构建心跳消息失败: " + e.message)

        }
    }


    /**
     * WebSocket事件监听器
     */
    private class WebSocketEventListener : WebSocketListener() {
        override fun onOpen(webSocket: WebSocket, response: Response) {

            updateConnectionState(com.huiver.wen.base.net.websocket.ConnectionState.CONNECTED)
            sendHandshake(token!!)
            startHeartBeat()
        }

        override fun onMessage(webSocket: WebSocket, text: String) {
            LogUtil.e("收到消息: $text")
            handleMessage(text)
        }

        override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
            LogUtil.e("WebSocket连接失败: " + t.message)
            updateConnectionState(com.huiver.wen.base.net.websocket.ConnectionState.DISCONNECTED)
            reconnect()
        }

        override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
            LogUtil.e("WebSocket正在关闭: $reason")
            webSocket.close(CLOSE_WEB_SOCKET, null)
            updateConnectionState(com.huiver.wen.base.net.websocket.ConnectionState.DISCONNECTED)
        }

        override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
            LogUtil.e("WebSocket已关闭: $reason")
            updateConnectionState(com.huiver.wen.base.net.websocket.ConnectionState.DISCONNECTED)
        }
    }


    /**
     * 处理收到的消息
     * @param message 消息内容
     */
    private fun handleMessage(message: String) {
        try {
            LogUtil.e("wanglaoji","未知类型的消息: $message")
            val jsonObject = JSONObject(message)
            val type = jsonObject.getInt("type")
            val webSocketCode = WebSocketCode.getWebSocketCodeByCode(type)

            if (webSocketCode == null) {
                LogUtil.e("未知类型的消息: $message")
                return
            }

            val data = jsonObject.optString("data", "")

            when (webSocketCode) {
                WebSocketCode.CONNECT_SUCCESS ->  {
                    LogUtil.e("WebSocket连接成功")
                }
                WebSocketCode.HEARTBEAT -> {
                    LogUtil.e("收到心跳响应，重置心跳计数")
                    heartBeatCount.set(0) // 收到心跳响应，重置计数
                }

                WebSocketCode.TOKEN_EXPIRED ->{

                }

                else -> {
                    // 通知监听器
                    if (messageListener != null) {
                        messageListener!!.onMessage(webSocketCode, data)
                    }
                    LogUtil.e("收到消息: $webSocketCode, 数据: $data")
                }
            }
        } catch (e: java.lang.Exception) {
            LogUtil.e("消息解析失败: " + e.message)
        }
    }

}