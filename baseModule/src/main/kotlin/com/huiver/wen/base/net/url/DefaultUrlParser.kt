package com.huiver.wen.base.net.url

import com.huiver.wen.base.net.parser.RetrofitUrlManager
import com.huiver.wen.base.net.parser.RetrofitUrlManager.IDENTIFICATION_PATH_SIZE
import okhttp3.HttpUrl
import kotlin.concurrent.Volatile


class DefaultUrlParser:UrlParser {

    private lateinit var mDomainUrlParser: UrlParser

    @Volatile
    private var mAdvancedUrlParser: UrlParser? = null

    @Volatile
    private var mSuperUrlParser: UrlParser? = null
    private lateinit var mRetrofitUrlManager: RetrofitUrlManager
    override fun init(retrofitUrlManager: RetrofitUrlManager) {
        this.mRetrofitUrlManager = retrofitUrlManager
        this.mDomainUrlParser = DomainUrlParser()
        mDomainUrlParser.init(retrofitUrlManager)
    }

    override fun parseUrl(domainUrl: HttpUrl?, url: HttpUrl?): HttpUrl? {
        if (url.toString().contains(IDENTIFICATION_PATH_SIZE)) {
            if (mSuperUrlParser == null) {
                synchronized(this) {
                    if (mSuperUrlParser == null) {
                        mSuperUrlParser = SuperUrlParser()
                        mSuperUrlParser?.init(mRetrofitUrlManager)
                    }
                }
            }
            return mSuperUrlParser!!.parseUrl(domainUrl, url)
        }


        //如果是高级模式则使用高级解析器
        if (mRetrofitUrlManager.isAdvancedModel()) {
            if (mAdvancedUrlParser == null) {
                synchronized(this) {
                    if (mAdvancedUrlParser == null) {
                        mAdvancedUrlParser = AdvancedUrlParser()
                        mAdvancedUrlParser?.init(mRetrofitUrlManager)
                    }
                }
            }
            return mAdvancedUrlParser!!.parseUrl(domainUrl, url)
        }

        return mDomainUrlParser.parseUrl(domainUrl, url)
    }
}