package com.huiver.wen.base.net.websocket

import android.os.Handler
import android.os.Looper
import com.huiver.wen.lib.common.LogUtil
import java.util.concurrent.CopyOnWriteArrayList

object WebSocketManager {
    private val webSocketHelper: WebSocketHelper = WebSocketHelper

    // 主线程Handler，用于回调到主线程
    private var mainHandler: Handler? = null

    // 是否已初始化
    private var isInitialized = false

    // 消息监听器列表，使用线程安全的集合
    private val messageCallbacks: CopyOnWriteArrayList<WebSocketMessageCallback> =
        CopyOnWriteArrayList()

    // 连接状态监听器列表
    private val stateCallbacks: CopyOnWriteArrayList<ConnectionStateCallback> =
        CopyOnWriteArrayList()

    init {
        mainHandler = Handler(Looper.getMainLooper())


        // 设置WebSocketHelper的消息监听器
        webSocketHelper.setMessageListener(object : WebSocketMessageListener {
            override fun onMessage(code: WebSocketCode, data: String) {
                handleWebSocketMessage(code, data)
            }

            override fun onConnectionStateChanged(state: com.huiver.wen.base.net.websocket.ConnectionState) {
                notifyConnectionStateChanged(state)
            }
        })
    }

    /**
     * 初始化WebSocket连接
     * 注意：必须先登录成功才能调用此方法
     * @return 是否成功初始化
     */
    fun initialize(): Boolean {
        if (isInitialized) {
            LogUtil.d("WebSocket已经初始化")
            return true
        }


        // 连接WebSocket
        webSocketHelper.firstConnect("1867756295126200322", "eyJhbGciOiJIUzUxMiJ9.eyJwYXNzd29yZCI6IiQyYSQxMCRmL2FwTlFva0NBdHc2bEdVWnZWVk9lVHJpay9KbzhMRUJsUDExb3dFVERVeGcuVVhmM3dYSyIsInVzZXJfaWQiOjI0OCwibG9naW5fdXNlcl9rZXkiOiI4OGNlYmZiOS1kODgyLTRmMmMtODA4OC0wYzQ2ZGNjY2YwYWEiLCJ1c2VybmFtZSI6IjE5OTE4MjA1OTIwIn0.w7UMOVmFd397De6YCBl-Fuxjc5MxwychvXSu0W9pR0l606114o3tnKTP_n9rjUN0_OmS7Yt8CBUvpCelYFZXIQ")
        isInitialized = true
        LogUtil.d("WebSocket初始化成功")
        return true
    }

    fun reconnect() {
        if (!isInitialized) {
            if (!initialize()) {
                LogUtil.d("重连失败：未初始化")
                return
            }
        } else {
            webSocketHelper.reconnect()
            LogUtil.d("开始重新连接WebSocket")
        }
    }

    /**
     * 处理WebSocket消息
     * @param code 消息类型
     * @param data 消息数据
     */
    private fun handleWebSocketMessage(code: WebSocketCode, data: String) {
        // 在主线程通知所有监听器
        mainHandler?.post {
            for (callback in messageCallbacks) {
                callback.onMessage(code, data)
            }
        }
    }

    /**
     * 通知所有连接状态监听器
     * @param state 连接状态
     */
    private fun notifyConnectionStateChanged(state: com.huiver.wen.base.net.websocket.ConnectionState) {
        mainHandler?.post {
            for (callback in stateCallbacks) {
                callback.onConnectionStateChanged(state)
            }
        }
    }

    /**
     * 添加消息监听器
     * @param callback 监听器
     */
    fun addMessageCallback(callback: WebSocketMessageCallback?) {
        if (callback != null && !messageCallbacks.contains(callback)) {
            messageCallbacks.add(callback)

        }
    }

    /**
     * 移除消息监听器
     * @param callback 监听器
     */
    fun removeMessageCallback(callback: WebSocketMessageCallback?) {
        messageCallbacks.remove(callback)
    }

    /**
     * 添加连接状态监听器
     * @param callback 监听器
     */
    fun addConnectionStateCallback(callback: ConnectionStateCallback?) {
        if (callback != null && !stateCallbacks.contains(callback)) {
            stateCallbacks.add(callback)
        }
    }

    /**
     * 移除连接状态监听器
     * @param callback 监听器
     */
    fun removeConnectionStateCallback(callback: ConnectionStateCallback?) {
        stateCallbacks.remove(callback)
    }

}
