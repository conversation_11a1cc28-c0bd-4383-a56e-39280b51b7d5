package com.huiver.wen.extension

import android.graphics.Typeface
import android.view.View
import android.widget.Checkable
import android.widget.TextView

//点击事件
var lastClickTime:Long =0
fun <T : View> T.singleClick(block: (T) -> Unit) = setOnClickListener {
    val currentTimeMillis = System.currentTimeMillis()
    if (currentTimeMillis - lastClickTime > 500 || this is Checkable) {
        lastClickTime = currentTimeMillis
        block(it as T)
    }
}

fun TextView.setTextBold(){
    setTypeface(null, Typeface.BOLD)
}

