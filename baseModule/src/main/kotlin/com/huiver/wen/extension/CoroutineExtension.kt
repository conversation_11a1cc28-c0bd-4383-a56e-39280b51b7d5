package com.huiver.wen.extension

import androidx.lifecycle.viewModelScope
import com.huiver.wen.viewmodel.BaseViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


/**
 * @param errorCode 错误码
 * @param errMsg 简要错误信息
 * @param report 是否需要上报
 * @param block 需要执行的任务
 */
fun BaseViewModel.requestMain(
    errorCode: Int = -1, errMsg: String = "", report: Boolean = false,
     block: suspend CoroutineScope.() -> Unit) {
    viewModelScope.launch(provider.dispatcherMain) {
        block.invoke(this)
    }
}

/**
 * @param errorCode 错误码
 * @param errMsg 简要错误信息
 * @param report 是否需要上报
 * @param block 需要执行的任务
 */
inline fun BaseViewModel.requestIO(
    errorCode: Int = -1, errMsg: String = "", report: Boolean = false,
    noinline block: suspend CoroutineScope.() -> Unit) {
    viewModelScope.launch(provider.dispatcherIO) {
        block.invoke(this)
    }
}

/**
 * @param errorCode 错误码
 * @param errMsg 简要错误信息
 * @param report 是否需要上报
 * @param delayTime 延时时间
 * @param block 需要执行的任务
 */
inline fun BaseViewModel.delayMain(
    delayTime: Long, errorCode: Int = -1, errMsg: String = "", report: Boolean = false,
    noinline block: suspend CoroutineScope.() -> Unit) {
    viewModelScope.launch(provider.dispatcherMain) {
        withContext(provider.dispatcherIO) {
            delay(delayTime)
        }
        block.invoke(this)
    }
}
