package com.huiver.wen.extension

import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter4.BaseQuickAdapter
import com.huiver.wen.base.module.R
import com.huiver.wen.base.ui.widget.CommonEmptyView

/**
 * <AUTHOR> by <PERSON><PERSON> on 2025/7/15.
 *
 **/

fun <T:Any, VH : RecyclerView.ViewHolder> BaseQuickAdapter<T, VH>.getSafePosition(position:Int):T?{

    if (position<0||position>=items.size){
        return null
    }
    return getItem(position)
}

/**
 * 判断position 是否在RecyclerView列表内容集合安全范围
 */
fun <T:Any, VH : RecyclerView.ViewHolder> BaseQuickAdapter<T, VH>.isSafeItemPosition(position: Int): Boolean{
    return position>=0&& position<items.size
}

fun <T:Any, VH : RecyclerView.ViewHolder> BaseQuickAdapter<T, VH>.setErrorDataSource(callback: () -> Unit){
    val commonEmptyView = CommonEmptyView(context)


    commonEmptyView.getEmptyContentTextView().isGone  = true
    commonEmptyView.getReloadView().apply {
        text = context.getText(R.string.base_reload)
        isVisible = true

        singleClick {
            callback.invoke()
        }
    }
    stateView = commonEmptyView


}

/**
 * 移除所有Decoration
 */
fun RecyclerView.removeAllDecorations(){
    for (index in 0 until itemDecorationCount){
        removeItemDecorationAt(index)
    }
}
