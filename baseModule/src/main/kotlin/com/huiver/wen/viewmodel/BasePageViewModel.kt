package com.huiver.wen.viewmodel

import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter4.BaseQuickAdapter
import com.chad.library.adapter4.QuickAdapterHelper
import com.chad.library.adapter4.loadState.LoadState
import com.chad.library.adapter4.loadState.trailing.TrailingLoadStateAdapter
import com.huiver.wen.base.common.data.PageSizeData
import com.huiver.wen.base.net.http.BaseResponse
import com.huiver.wen.extension.requestIO
import com.huiver.wen.extension.requestMain
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

abstract class BasePageViewModel<T: Any,VH : RecyclerView.ViewHolder> (private val adapter: BaseQuickAdapter<T,VH >):BaseViewModel(){
    private val mPageData = PageSizeData()
    private var mRefreshFlag = true
    private val mAdapterHelper: QuickAdapterHelper by lazy {  QuickAdapterHelper.Builder(adapter).setTrailingLoadStateAdapter(object :TrailingLoadStateAdapter.OnTrailingListener{
        override fun onLoad() {
            loadData()


        }

        override fun onFailRetry() {
            loadData()
        }

        override fun isAllowLoading(): Boolean {
            return !mRefreshFlag
        }
    }).build() }

    //协程锁
    private val mutex = Mutex()

    /**
     * 设置pageSize
     */
    protected fun setPageSize(pageSize: Int) {
        mPageData.limit = pageSize
    }

    /**重置分页*/
    fun resetPageSizeState() {
        mPageData.resetPage()

    }

    /**
     * 重新加载数据
     */
    fun refresh() {
        mRefreshFlag = true
        loadDataSync(true)
    }

    /**
     * 加载数据
     */
    private fun loadData() {
        loadDataSync(false)
    }

    /**
     * 加载更多数据
     */
    private fun loadMoreData() {
        loadData()
    }

    /**
     * 加载列表数据
     * @param pageData 使用了 copy 函数  防止子类手动修改 pageSize数据
     */
    protected abstract suspend fun onRequestNet(pageData: PageSizeData): BaseResponse<List<T>>

    /**
     * 加载数据
     */


    private suspend fun request() {
        val response = onRequestNet(mPageData)
        parseData(response)
    }

    //异步加载
    private fun loadDataSync(reload: Boolean = false) {
        requestIO {
            mutex.withLock {
                if (reload){
                    resetPageSizeState()
                }
                request()
            }
        }

    }
    private fun parseData(response: BaseResponse<List<T>>) {
        requestMain {
            if (response.isSuccess()){
                if (mRefreshFlag){
                    mRefreshFlag = false
                }
                val responseData = response.data ?: emptyList()
                mPageData.setIsMore(responseData.size)
                if (mPageData.isFirstPage()){
                    adapter.submitList(responseData)
                }else{
                    adapter.addAll(responseData)
                }
                if (responseData.size < mPageData.limit){
                    // 设置状态为未加载，并且没有分页数据了
                    mAdapterHelper.trailingLoadState = LoadState.NotLoading(true)
                }else{
                    //设置状态为未加载，并且还有分页数据
                    mAdapterHelper.trailingLoadState = LoadState.NotLoading(false)
                }
                mPageData.addCurrentPage()
            }else{
                mAdapterHelper.trailingLoadState = LoadState.Error(Throwable(response.msg))
            }
        }


    }



}