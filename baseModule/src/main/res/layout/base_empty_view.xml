<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"

    android:layout_width="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/padding_63">

    <ImageView
        android:src="@mipmap/base_empty_image"
        android:id="@+id/iv_empty"
        android:scaleType="fitCenter"
        android:layout_width="@dimen/padding_200"
        android:layout_height="@dimen/padding_200"/>
    <TextView
        android:id="@+id/tv_content"
        android:textSize="@dimen/txt_14"
        android:gravity="center"
        android:layout_marginTop="@dimen/padding_24"
        android:text="@string/base_data_empty"

        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/tv_reload"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="@dimen/padding_117"
        android:paddingLeft="@dimen/padding_16"
        android:paddingRight="@dimen/padding_16"
        android:layout_marginTop="@dimen/padding_24"
        android:background="@drawable/base_rectangle_3e5dee_20"
        android:gravity="center"
        android:minHeight="@dimen/padding_44"
        android:text="@string/base_reload"
        android:visibility="gone"
        android:textColor="@android:color/white"
        android:textSize="@dimen/txt_14"
        tools:visibility="visible"
        />

</LinearLayout>