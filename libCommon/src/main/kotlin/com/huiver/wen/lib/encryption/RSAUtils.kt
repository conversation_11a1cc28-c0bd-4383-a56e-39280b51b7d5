package  com.huiver.wen.lib.encryption


import com.huiver.wen.lib.common.ConvertUtils
import java.math.BigInteger
import java.security.InvalidParameterException
import java.security.KeyFactory
import java.security.KeyPair
import java.security.KeyPairGenerator
import java.security.NoSuchAlgorithmException
import java.security.PrivateKey
import java.security.PublicKey
import java.security.SecureRandom
import java.security.interfaces.RSAPrivateKey
import java.security.interfaces.RSAPublicKey
import java.security.spec.InvalidKeySpecException
import java.security.spec.RSAPrivateKeySpec
import java.security.spec.RSAPublicKeySpec
import java.text.SimpleDateFormat
import java.util.Date
import javax.crypto.Cipher


/**
 * @Description:主要功能:RSA加密解密工具类,android平台上RSA加密算法用的默认Provider是“org.bouncycastle.jce.provider.BouncyCastleProvider”
 * @version: 1.0.0
 */
object RSAUtils {
    private const val ALGORITHOM = "RSA"

    // 算法名称
    private const val KEY_SIZE = 1024

    //密钥大小
    private var keyPairGen: KeyPairGenerator? = null
    private var keyFactory: KeyFactory? = null

    /**
     * 缓存的密钥对。
     */
    private var oneKeyPair: KeyPair? = null

    init {
        try {
            keyPairGen = KeyPairGenerator.getInstance(ALGORITHOM)
            keyFactory = KeyFactory.getInstance(ALGORITHOM)
        } catch (ex: NoSuchAlgorithmException) {
            println(ex.message)
        }
    }

    /**
     * 生成并返回RSA密钥对。
     * @return 生成并返回RSA密钥对。
     */
    @Synchronized
    private fun generateKeyPair(): KeyPair? {
        try {
            keyPairGen!!.initialize(
                KEY_SIZE,
                SecureRandom(SimpleDateFormat("yyyyMMdd").format(Date()).toByteArray())
            )
            oneKeyPair = keyPairGen!!.generateKeyPair()
            return oneKeyPair
        } catch (ex: InvalidParameterException) {
            println("KeyPairGenerator does not support a key length of " + KEY_SIZE + ".")
            ex.printStackTrace()
        } catch (ex: NullPointerException) {
            println("RSAUtils#KEY_PAIR_GEN is null, can not generate KeyPairGenerator instance.")
            ex.printStackTrace()
        }
        return null
    }

    /**
     * 返回RSA密钥对。
     *
     * @return 返回RSA密钥对。
     */
    val keyPair: KeyPair?
        get() = if (oneKeyPair != null) {
            oneKeyPair
        } else generateKeyPair()

    /**
     * 根据给定的系数和专用指数构造一个RSA专用的公钥对象。
     *
     * @param modulus        系数。
     * @param publicExponent 专用指数。
     * @return RSA专用公钥对象。
     */
    fun generateRSAPublicKey(modulus: ByteArray?, publicExponent: ByteArray?): RSAPublicKey? {
        val publicKeySpec = RSAPublicKeySpec(
            BigInteger(modulus),
            BigInteger(publicExponent)
        )
        try {
            return keyFactory?.generatePublic(publicKeySpec) as RSAPublicKey
        } catch (ex: InvalidKeySpecException) {
            println("RSAPublicKeySpec is unavailable.")
            ex.printStackTrace()
        } catch (ex: NullPointerException) {
            println("RSAUtils#KEY_FACTORY is null, can not generate KeyFactory instance.")
            ex.printStackTrace()
        }
        return null
    }

    /**
     * 根据给定的系数和专用指数构造一个RSA专用的私钥对象。
     *
     * @param modulus         系数。
     * @param privateExponent 专用指数。
     * @return RSA专用私钥对象。
     */
    fun generateRSAPrivateKey(modulus: ByteArray?, privateExponent: ByteArray?): RSAPrivateKey? {
        val privateKeySpec = RSAPrivateKeySpec(
            BigInteger(modulus),
            BigInteger(privateExponent)
        )
        try {
            return keyFactory!!.generatePrivate(privateKeySpec) as RSAPrivateKey
        } catch (ex: InvalidKeySpecException) {
            println("RSAPrivateKeySpec is unavailable.")
            ex.printStackTrace()
        } catch (ex: NullPointerException) {
            println("RSAUtils#KEY_FACTORY is null, can not generate KeyFactory instance.")
            ex.printStackTrace()
        }
        return null
    }

    /**
     * 根据给定的16进制系数和专用指数字符串构造一个RSA专用的私钥对象。
     *
     * @param hexModulus         系数。
     * @param hexPrivateExponent 专用指数。
     * @return RSA专用私钥对象。
     */
    fun getRSAPrivateKey(hexModulus: String, hexPrivateExponent: String): RSAPrivateKey? {

        var modulus: ByteArray? = null
        var privateExponent: ByteArray? = null
        try {
            modulus = ConvertUtils.hexStringToBytes(hexModulus)
            privateExponent = ConvertUtils.hexStringToBytes(hexPrivateExponent)
        } catch (ex: Exception) {
            println("hexModulus or hexPrivateExponent value is invalid. return null(RSAPrivateKey).")
            ex.printStackTrace()
        }
        return if (modulus != null && privateExponent != null) {
            generateRSAPrivateKey(modulus, privateExponent)
        } else null
    }

    /**
     * 根据给定的16进制系数和专用指数字符串构造一个RSA专用的公钥对象。
     *
     * @param hexModulus        系数。
     * @param hexPublicExponent 专用指数。
     * @return RSA专用公钥对象。
     */
    fun getRSAPublicKey(hexModulus: String, hexPublicExponent: String): RSAPublicKey? {

        var modulus: ByteArray? = null
        var publicExponent: ByteArray? = null
        try {
            modulus = ConvertUtils.hexStringToBytes(hexModulus)
            publicExponent = ConvertUtils.hexStringToBytes(hexPublicExponent)
        } catch (ex: Exception) {
            println("hexModulus or hexPublicExponent value is invalid. return null(RSAPublicKey).")
            ex.printStackTrace()
        }
        return if (modulus != null && publicExponent != null) {
            generateRSAPublicKey(modulus, publicExponent)
        } else null
    }

    /**
     * 使用指定的公钥加密数据。
     *
     * @param publicKey 给定的公钥。
     * @param data      要加密的数据。
     * @return 加密后的数据。
     */
    @Throws(Exception::class)
    fun encrypt(publicKey: PublicKey?, data: ByteArray?): ByteArray {
        val ci = Cipher.getInstance(ALGORITHOM)
        ci.init(Cipher.ENCRYPT_MODE, publicKey)
        return ci.doFinal(data)
    }

    /**
     * 使用指定的私钥解密数据。
     *
     * @param privateKey 给定的私钥。
     * @param data       要解密的数据。
     * @return 原数据。
     */
    @Throws(Exception::class)
    fun decrypt(privateKey: PrivateKey?, data: ByteArray?): ByteArray {
        val ci = Cipher.getInstance(ALGORITHOM)
        ci.init(Cipher.DECRYPT_MODE, privateKey)
        return ci.doFinal(data)
    }

    /**
     * 使用给定的公钥加密给定的字符串。
     *
     * @param publicKey 给定的公钥。
     * @param plaintext 字符串。
     * @return 给定字符串的密文。
     */
    fun encryptString(publicKey: PublicKey?, plaintext: String?): String? {
        if (publicKey == null || plaintext == null) {
            return null
        }
        val data = plaintext.toByteArray()
        try {
            val enData = encrypt(publicKey, data)
            return ConvertUtils.bytesToHexString(enData)
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
        return null
    }

    /**
     * 使用默认的公钥加密给定的字符串。
     *
     * @param plaintext 字符串
     * @return 给定字符串的密文
     */
    fun encryptString(plaintext: String?): String? {
        if (plaintext == null) {
            return null
        }
        val data = plaintext.toByteArray()
        val keyPair = keyPair
        try {
            val enData = encrypt(keyPair?.public as RSAPublicKey, data)
            return ConvertUtils.bytesToHexString(enData)
        } catch (ex: NullPointerException) {
            println("keyPair cannot be null.")
            ex.printStackTrace()
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
        return null
    }

    /**
     * 生成由JS的RSA加密的字符串。
     *
     * @param publicKey 公钥
     * @param plaintext 原文字符串
     * @return 加密后的字符串
     */
    fun encryptStringByJs(
        publicKey: PublicKey?,
        plaintext: String?
    ): String? {
        return if (plaintext == null) {
            null
        } else encryptString(publicKey, reverse(plaintext))
    }

    /**
     * 用默认公钥生成由JS的RSA加密的字符串。
     * @param plaintext 原文字符串
     * @return 加密后的字符串
     */
    fun encryptStringByJs(plaintext: String?): String? {
        return if (plaintext == null) {
            null
        } else encryptString(reverse(plaintext))
    }

    /**
     * 使用给定的私钥解密给定的字符串。
     *
     * 若私钥为 `null`，或者 `encrypttext` 为 `null`或空字符串则返回 `null`。
     * 私钥不匹配时，返回 `null`。
     *
     * @param privateKey  给定的私钥。
     * @param encrypttext 密文。
     * @return 原文字符串。
     */
    fun decryptString(privateKey: PrivateKey, encrypttext: String): String? {

        try {
            val enData: ByteArray? = ConvertUtils.hexStringToBytes(encrypttext)
            val data = decrypt(privateKey, enData)
            return String(data)
        } catch (ex: Exception) {
            println(
                String.format(
                    "\"%s\" Decryption failed. Cause: %s",
                    encrypttext,
                    ex.cause!!.message
                )
            )
        }
        return null
    }

    /**
     * 使用默认的私钥解密给定的字符串。
     *
     * @param encrypttext 密文。
     * @return 原文字符串。
     */
    fun decryptString(encrypttext: String?): String? {
        if (encrypttext.isNullOrBlank()) {
            return null
        }
        val keyPair = keyPair
        try {
            val enData: ByteArray? = ConvertUtils.hexStringToBytes(encrypttext)
            val data = decrypt(keyPair!!.private as RSAPrivateKey, enData)
            return String(data)
        } catch (ex: NullPointerException) {
            println("keyPair cannot be null.")
            ex.printStackTrace()
        } catch (ex: Exception) {
            println(String.format("\"%s\" Decryption failed. Cause: %s", encrypttext, ex.message))
            ex.printStackTrace()
        }
        return null
    }

    /**
     * 使用指定的私钥解密由JS加密的字符串。
     *
     * @param privateKey  私钥
     * @param encryptText 密文
     * @return `encrypttext` 的原文字符串
     */
    fun decryptStringByJs(privateKey: PrivateKey, encryptText: String): String? {
        val text = decryptString(privateKey, encryptText) ?: return null
        return reverse(text)
    }

    fun decryptStringByJs(encrypttext: String?): String? {
        val text = decryptString(encrypttext) ?: return null
        return reverse(text)
    }

    /**
     * 返回已初始化的默认的公钥。
     * @return 返回已初始化的默认的公钥。
     */
    val defaultPublicKey: RSAPublicKey?
        get() {
            val keyPair = keyPair
            return if (keyPair != null) {
                keyPair.public as RSAPublicKey
            } else null
        }

    /**
     * 返回已初始化的默认的私钥。
     * @return 返回已初始化的默认的私钥。
     */
    val defaultPrivateKey: RSAPrivateKey?
        get() {
            val keyPair = keyPair
            return if (keyPair != null) {
                keyPair.private as RSAPrivateKey
            } else null
        }

    /**
     * 逆转字符串
     * @param str 待逆转的字符串
     * @return 逆转后字符串
     */
    private fun reverse(str: String?): String? {
        return if (str == null) {
            null
        } else StringBuilder(str).reverse().toString()
    }

}