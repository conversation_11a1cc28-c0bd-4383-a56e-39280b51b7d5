package com.huiver.wen.lib.encryption

import com.huiver.wen.lib.common.ConvertUtils
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.nio.ByteBuffer
import java.nio.channels.FileChannel
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.util.Locale

/**
 *  Created by Huiver on 2025/6/14.
 *
 *
 */


object MD5Utils {
    fun encryptMD5(securityStr: String): String {
        val data = securityStr.toByteArray()
        var md5: MessageDigest? = null
        try {
            md5 = MessageDigest.getInstance("MD5")
            md5.update(data)
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        }
        val resultBytes = md5!!.digest()
        val builder = StringBuilder()
        for (i in resultBytes.indices) {
            if (Integer.toHexString(0xFF and resultBytes[i].toInt()).length == 1) {
                builder.append("0").append(
                    Integer.toHexString(0xFF and resultBytes[i].toInt())
                )
            } else {
                builder.append(Integer.toHexString(0xFF and resultBytes[i].toInt()))
            }
        }
        return builder.toString()
    }

    /**
     * 获得字符串的md5大写值
     *
     * @param str 待加密的字符串
     * @return md5加密后的大写字符串
     */
    fun encryptMD5Uppercase(str: String): String {
        return encryptMD5(str).uppercase(Locale.getDefault())
    }

    /**
     * 获得文件的md5值
     *
     * @param file 文件对象
     * @return 文件的md5
     */
    fun getFileDM5(file: File): String {
        var ret = ""
        var inputStream: FileInputStream? = null
        var ch: FileChannel? = null
        try {
            inputStream = FileInputStream(file)
            ch = inputStream.channel
            val byteBuffer: ByteBuffer = ch.map(
                FileChannel.MapMode.READ_ONLY, 0,
                file.length()
            )
            val md5 = MessageDigest.getInstance("MD5")
            md5.update(byteBuffer)
            ret = ConvertUtils.bytesToHexString(md5.digest()).toString()
        } catch (e: IOException) {
            e.printStackTrace()
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
            if (ch != null) {
                try {
                    ch.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        return ret
    }

    /**
     * 获得文件md5值大写字符串
     *
     * @param file 文件对象
     * @return 文件md5大写字符串
     */
    fun getFileUppercaseMD5(file: File): String {
        return getFileDM5(file).uppercase(Locale.getDefault())
    }

    /**
     * 校验文件的md5值
     *
     * @param file 目标文件
     * @param md5  基准md5
     * @return 校验结果
     */
    fun checkFileMD5(file: File, md5: String?): Boolean {
        return getFileDM5(file).equals(md5, ignoreCase = true)
    }

    /**
     * 校验字符串的md5值
     *
     * @param str 目标字符串
     * @param md5 基准md5
     * @return 校验结果
     */
    fun checkMD5(str: String, md5: String?): Boolean {
        return encryptMD5(str).equals(md5, ignoreCase = true)
    }

    /**
     * 获得加盐md5，算法过程是原字符串md5后连接加盐字符串后再进行md5
     *
     * @param str  待加密的字符串
     * @param salt 盐
     * @return 加盐md5
     */
    fun encryptMD5(str: String, salt: String): String {
        return encryptMD5(encryptMD5(str) + salt)
    }
}