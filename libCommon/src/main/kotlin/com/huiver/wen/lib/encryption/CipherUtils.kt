package com.huiver.wen.lib.encryption

import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.io.InputStream
import java.security.DigestInputStream
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException


class CipherUtils {
    fun md5(inputStream: InputStream?): String? {
        val bufferSize = 256 * 1024
        var digestInputStream: DigestInputStream? = null
        try {
            var messageDigest = MessageDigest.getInstance("MD5")
            digestInputStream = DigestInputStream(inputStream, messageDigest)
            val buffer = ByteArray(bufferSize)
            while (digestInputStream.read(buffer) > 0)
            messageDigest = digestInputStream.messageDigest
            val resultByteArray = messageDigest.digest()
            val hexDigits = charArrayOf(
                '0',
                '1',
                '2',
                '3',
                '4',
                '5',
                '6',
                '7',
                '8',
                '9',
                'A',
                'B',
                'C',
                'D',
                'E',
                'F'
            )
            val resultCharArray = CharArray(resultByteArray.size * 2)
            var index = 0
            for (b in resultByteArray) {
                resultCharArray[index++] = hexDigits[b.toInt() ushr 4 and 0xf]
                resultCharArray[index++] = hexDigits[b.toInt() and 0xf]
            }
            return String(resultCharArray)
        } catch (e: NoSuchAlgorithmException) {
            return null
        } catch (e: IOException) {
            e.printStackTrace()
        } finally {
            try {
                digestInputStream?.close()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return null
    }

    /**
     * 异或加密
     * @param str
     * @param privateKey
     * @return
     */
    fun XorEncode(str: String, privateKey: String): String? {
        val snNum = IntArray(str.length)
        var result: String? = ""
        var temp = ""
        var i = 0
        var j = 0
        while (i < str.length) {
            if (j == privateKey.length) j = 0
            snNum[i] = str[i].code xor privateKey[j].code
            i++
            j++
        }
        for (k in 0 until str.length) {
            if (snNum[k] < 10) {
                temp = "00" + snNum[k]
            } else {
                if (snNum[k] < 100) {
                    temp = "0" + snNum[k]
                }
            }
            result += temp
        }
        return result
    }

    /**
     * 异或解密
     * @param str
     * @param privateKey
     * @return
     */
    fun XorDecode(str: String, privateKey: String): String {
        val snNum = CharArray(str.length / 3)
        var result = ""
        var i = 0
        var j = 0
        while (i < str.length / 3) {
            if (j == privateKey.length) j = 0
            val n = str.substring(i * 3, i * 3 + 3).toInt()
            snNum[i] = (n.toChar().code xor privateKey[j].code).toChar()
            i++
            j++
        }
        for (k in 0 until str.length / 3) {
            result += snNum[k]
        }
        return result
    }

    /**
     * 字符串sha1值
     * @param str
     * @return
     */
    fun sha1(str: String): String {
        try {
            val digest = MessageDigest.getInstance("SHA-1")
            digest.update(str.toByteArray())
            val messageDigest = digest.digest()
            val hexString = StringBuilder()
            for (aMessageDigest in messageDigest) {
                val shaHex = Integer.toHexString(aMessageDigest.toInt() and 0xFF)
                if (shaHex.length < 2) {
                    hexString.append(0)
                }
                hexString.append(shaHex)
            }
            return hexString.toString()
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        }
        return ""
    }

    /**
     * 文件hash校验
     * @param file
     * @return
     */
    fun sha1(file: File?): String? {
        var inputStream: FileInputStream? = null
        try {
            inputStream = FileInputStream(file)
            val messageDigest = MessageDigest.getInstance("SHA-1")
            val b = ByteArray(1024 * 1024 * 10) //10M memory
            var len: Int
            while (inputStream.read(b).also { len = it } > 0) {
                messageDigest.update(b, 0, len)
            }
            return byte2Hex(messageDigest.digest())
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        } catch (e: IOException) {
            e.printStackTrace()
        } finally {
            inputStream?.close()
        }
        return null
    }

    private fun byte2Hex(b: ByteArray): String {
        val sb = StringBuilder()
        for (aB in b) {
            val s = Integer.toHexString(aB.toInt() and 0xFF)
            if (s.length == 1) {
                sb.append("0")
            }
            //sb.append(s.toUpperCase());
            sb.append(s)
        }
        return sb.toString()
    }
}