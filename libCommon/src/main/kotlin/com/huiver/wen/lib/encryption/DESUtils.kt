package com.huiver.wen.lib.encryption

import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec

/**
 *  Created by <PERSON><PERSON> on 2025/6/14.
 *
 *
 */

class DESUtils private constructor() {
    init {
        throw UnsupportedOperationException("cannot be instantiated")
    }

    companion object {
        /*
     * 生成密钥
     */
        @Throws(Exception::class)
        fun initKey(): ByteArray {
            val keyGen = KeyGenerator.getInstance("DES")
            keyGen.init(56)
            val secretKey = keyGen.generateKey()
            return secretKey.encoded
        }

        /*
     * DES 加密
     */
        @Throws(Exception::class)
        fun encrypt(data: ByteArray?, key: ByteArray?): ByteArray {
            val secretKey: SecretKey = SecretKeySpec(key, "DES")
            val cipher =
                Cipher.getInstance("DES/ECB/PKCS5Padding")
            cipher.init(Cipher.ENCRYPT_MODE, secretKey)
            return cipher.doFinal(data)
        }

        /*
     * DES 解密
     */
        @Throws(Exception::class)
        fun decrypt(data: ByteArray?, key: ByteArray?): ByteArray {
            val secretKey: SecretKey = SecretKeySpec(key, "DES")
            val cipher =
                Cipher.getInstance("DES/ECB/PKCS5Padding")
            cipher.init(Cipher.DECRYPT_MODE, secretKey)
            return cipher.doFinal(data)
        }
    }
}