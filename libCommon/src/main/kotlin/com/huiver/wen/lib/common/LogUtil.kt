package com.huiver.wen.lib.common

import android.os.Environment
import android.util.Log
import com.tencent.bugly.crashreport.CrashReport
import org.jetbrains.annotations.NonNls
import timber.log.Timber
import timber.log.Timber.Tree
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

object LogUtil {

    @JvmStatic fun plant (isRelease:Boolean){
        if (isRelease){
            Timber.plant(Timber.DebugTree(), FileLoggingTree().apply {
                cleanOldLogs(30)
            })
        }else{
            Timber.plant(Timber.DebugTree())
        }

    }


    @JvmStatic fun v(tag: String, @NonNls message: String?, vararg args: Any?,t: Throwable?= null){
        Timber.tag(tag).v(t,message,args)

    }
    @JvmStatic fun v( @NonNls message: String?, vararg args: Any?,t: Throwable?= null){

        Timber.v(t,message,args)

    }
    @JvmStatic fun e(tag: String, @NonNls message: String?, vararg args: Any?,t: Throwable?= null){
        Timber.tag(tag).e(t,message,args)

    }
    @JvmStatic fun e( @NonNls message: String?, vararg args: Any?,t: Throwable?= null){
        Timber.e(t,message,args)

    }
    @JvmStatic fun w(tag: String, @NonNls message: String?, vararg args: Any?,t: Throwable?= null){
        Timber.tag(tag).w(t,message,args)

    }
    @JvmStatic fun w( @NonNls message: String?, vararg args: Any?,t: Throwable?= null){
        Timber.w(t,message,args)

    }
    @JvmStatic fun log(tag: String,priority: Int, @NonNls message: String?, vararg args: Any?,t: Throwable?= null){
        Timber.tag(tag).log(priority,message,args, t)
    }
    @JvmStatic fun log(priority: Int, @NonNls message: String?, vararg args: Any?,t: Throwable?= null){
        Timber.log(priority,message,args, t)
    }
    @JvmStatic fun wtf(tag: String,@NonNls message: String?, vararg args: Any?,t: Throwable?= null){
        Timber.tag(tag).wtf(message,args, t)


    }
    @JvmStatic fun wtf(@NonNls message: String?, vararg args: Any?,t: Throwable?= null){
        Timber.wtf(message,args, t)


    }

    @JvmStatic fun d(tag: String, @NonNls message: String?, vararg args: Any?,t: Throwable?= null){
        Timber.tag(tag).d(t,message,args)

    }
    @JvmStatic fun d( @NonNls message: String?, vararg args: Any?,t: Throwable?= null){
        Timber.d(t,message,args)

    }
    @JvmStatic fun i(tag: String, @NonNls message: String?, vararg args: Any?,t: Throwable?= null){
        Timber.tag(tag).i(t,message,args)

    }
    @JvmStatic fun i( @NonNls message: String?, vararg args: Any?,t: Throwable?= null){
        Timber.i(t,message,args)

    }



    class FileLoggingTree : Tree() {

        // 单线程池，确保日志顺序写入
        private val executor: ExecutorService = Executors.newSingleThreadExecutor()

        // 日志文件目录（示例：/sdcard/Android/data/packageName/files/logs）
        private val logDir: File by lazy {
            val extDir = Environment.getExternalStorageDirectory()
            File(extDir, "logs").apply {
                if (!exists()) mkdirs()
            }
        }

        // 当前日志文件（按日期命名）
        private val logFile: File by lazy {
            val date = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
            File(logDir, "app_$date.log")
        }

        override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
            if (priority < Log.INFO) return // 只记录 INFO 及以上级别的日志（按需调整）

            executor.execute {
                try {
                    val logMsg = buildLogMessage(priority, tag, message, t)
                    writeToFile(logMsg)
                } catch (e: IOException) {
                    Timber.e(e, "Failed to write log to file")
                }
            }
        }

        // 构建日志行（格式：时间 级别/TAG: 消息）
        private fun buildLogMessage(
            priority: Int,
            tag: String?,
            message: String,
            t: Throwable?
        ): String {
            val time = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault()).format(Date())
            val level = when (priority) {
                Log.VERBOSE -> "V"
                Log.DEBUG -> "D"
                Log.INFO -> "I"
                Log.WARN -> "W"
                Log.ERROR -> "E"
                else -> "?"
            }
            return StringBuilder()
                .append("$time $level/${tag ?: "?"}: $message")
                .apply { if (t != null) append("\n${Log.getStackTraceString(t)}") }
                .append("\n") // 换行
                .toString()
        }

        // 写入文件
        private fun writeToFile(message: String) {
            FileWriter(logFile, true).use { writer ->
                writer.append(message)
                writer.flush()
            }
        }

        // 可选：清理过期日志（如保留最近7天）
        fun cleanOldLogs(retentionDays: Int = 7) {
            val cutoff = System.currentTimeMillis() - retentionDays * 24 * 60 * 60 * 1000L
            logDir.listFiles()?.forEach { file ->
                if (file.lastModified() < cutoff) {
                    file.delete()
                }
            }
        }
    }

    private class ErrorReportingTree: Tree(){
        override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
            if (priority== Log.ERROR){
                CrashReport.postCatchedException(t)

            }

        }
    }


}