package com.huiver.wen.lib.encryption

import java.security.MessageDigest
import java.security.NoSuchAlgorithmException


object SHAUtils {
    /**
     * SHA-512 加密
     * @param data
     * @return
     */
    fun encryptSHA(data: ByteArray): String {
        var sha: MessageDigest? = null
        try {
            sha = MessageDigest.getInstance("SHA-512")
            sha.update(data)
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        }
        val resultBytes = sha!!.digest()
        val builder = StringBuilder()
        for (i in resultBytes.indices) {
            if (Integer.toHexString(0xFF and resultBytes[i].toInt()).length == 1) {
                builder.append("0").append(
                    Integer.toHexString(0xFF and resultBytes[i].toInt())
                )
            } else {
                builder.append(Integer.toHexString(0xFF and resultBytes[i].toInt()))
            }
        }
        return builder.toString()
    }
}