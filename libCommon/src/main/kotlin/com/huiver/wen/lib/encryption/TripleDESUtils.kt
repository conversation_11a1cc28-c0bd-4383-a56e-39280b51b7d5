package com.huiver.wen.lib.encryption

import java.security.NoSuchAlgorithmException
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec


object TripleDESUtils {

    @Throws(NoSuchAlgorithmException::class)
    fun initKey(): ByteArray {
        val keyGen = KeyGenerator.getInstance("DESede")
        keyGen.init(168) //112 168
        val secretKey = keyGen.generateKey()
        return secretKey.encoded
    }

    /*
 * 3DES 加密
 */
    @Throws(Exception::class)
    fun encrypt(data: ByteArray?, key: ByteArray?): ByteArray {
        val secretKey: SecretKey =
            SecretKeySpec(key, "DESede")
        val cipher =
            Cipher.getInstance("DESede/ECB/PKCS5Padding")
        cipher.init(Cipher.ENCRYPT_MODE, secretKey)
        return cipher.doFinal(data)
    }

    /*
 * 3DES 解密
 */
    @Throws(Exception::class)
    fun decrypt(data: ByteArray?, key: ByteArray?): ByteArray {
        val secretKey: SecretKey =
            SecretKeySpec(key, "DESede")
        val cipher =
            Cipher.getInstance("DESede/ECB/PKCS5Padding")
        cipher.init(Cipher.DECRYPT_MODE, secretKey)
        return cipher.doFinal(data)
    }
}