package com.huiver.wen.lib.common

import android.annotation.SuppressLint
import android.text.TextUtils
import java.util.Locale

/**
 *  Created by <PERSON><PERSON> on 2025/6/14.
 *
 *
 */


object ConvertUtils {


    /**
     * 十六进制字符串转换为byte数组
     *
     * @param hexString
     * @return
     */
    fun hexStringToBytes(source: String?): ByteArray? {
        if (TextUtils.isEmpty(source)){
            return null
        }
        var hexString = source!!

        hexString = hexString.uppercase(Locale.getDefault())
        val length = hexString.length / 2
        val hexChars = hexString.toCharArray()
        val d = ByteArray(length)
        for (i in 0 until length) {
            val pos = i * 2
            d[i] = (charToByte(hexChars[pos]).toInt() shl 4 or charToByte(
                hexChars[pos + 1]
            ).toInt()).toByte()
        }
        return d
    }

    /**
     * char转换为byte数组
     * @param c
     * @return
     */
    fun charToByte(c: Char): Byte {
        return "0123456789ABCDEF".indexOf(c).toByte()
    }

    /**
     * 16进制转化为数字
     * @param ch 16进制
     * @param index 索引
     * @return 转化结果
     * @throws Exception 转化失败异常
     */
    @Throws(Exception::class)
    private fun toDigit(ch: Char, index: Int): Int {
        val digit = ch.digitToIntOrNull(16) ?: -1
        if (digit == -1) {
            throw Exception(
                "Illegal hexadecimal character " + ch
                        + " at index " + index
            )
        }
        return digit
    }

    /**
     * bytes数组转16进制String
     * @param data bytes数组
     * @param toDigits DIGITS_LOWER或DIGITS_UPPER
     * @return 转化结果
     */
    private fun bytes2Hex(data: ByteArray, toDigits: CharArray): String {
        val l = data.size
        val out = CharArray(l shl 1)
        // two characters form the hex value.
        var i = 0
        var j = 0
        while (i < l) {
            out[j++] = toDigits[0xF0 and data[i].toInt() ushr 4]
            out[j++] = toDigits[0x0F and data[i].toInt()]
            i++
        }
        return String(out)
    }

    /**
     * byte数组转换为十六进制字符串
     *
     * @param b
     * @return
     */
    fun bytesToHexString(b: ByteArray): String? {
        if (b.size == 0) {
            return null
        }
        val sb = StringBuilder("")
        for (i in b.indices) {
            val value = b[i].toInt() and 0xFF
            val hv = Integer.toHexString(value)
            if (hv.length < 2) {
                sb.append(0)
            }
            sb.append(hv)
        }
        return sb.toString()
    }

    /**
     * int转换为byte数组
     *
     * @param res
     * @return
     */
    fun intToByte(res: Int): ByteArray {
        val targets = ByteArray(4)
        targets[0] = (res and 0xff).toByte() // 最低位
        targets[1] = (res shr 8 and 0xff).toByte() // 次低位
        targets[2] = (res shr 16 and 0xff).toByte() // 次高位
        targets[3] = (res ushr 24).toByte() // 最高位,无符号右移。
        return targets
    }

    /**
     * byte数组转换为int
     *
     * @param res
     * @return
     */
    fun byteToInt(res: ByteArray): Int {
        // 一个byte数据左移24位变成0x??000000，再右移8位变成0x00??0000
        return res[3].toInt() and 0xff or (res[2]
            .toInt() shl 8 and 0xff00) or (res[1]
            .toInt() shl 16 and 0xff0000) or (res[0].toInt() shl 24 and -0x1000000)
    }

    /**
     * 保留几位小数
     */
    @SuppressLint("DefaultLocale")
    fun saveDecimals(cnt: Int, value: Double): String {
        return if (cnt == 2) String.format(
            "%.02f",
            value
        ) else if (cnt == 1) String.format("%.01f", value) else String.format("%.0f", value)
    }



    /**
     * String转Byte
     * @param str
     * @return
     */
    fun stringToByte(str: String?): Byte {
        var b: Byte = 0
        if (str != null) {
            try {
                b = str.toByte()
            } catch (_: Exception) {
            }
        }
        return b
    }

    /**
     * String转Boolean
     * @param str
     * @return
     */
    fun stringToBoolean(str: String?): Boolean {
        return if (str == null) {
            false
        } else {
            if (str == "1") {
                true
            } else if (str == "0") {
                false
            } else {
                try {
                    java.lang.Boolean.parseBoolean(str)
                } catch (e: Exception) {
                    false
                }
            }
        }
    }

    /**
     * String转Int
     * @param str
     * @return
     */
    fun stringToInt(str: String?): Int {
        var i = 0
        i = if (str != null) {
            try {
                str.trim { it <= ' ' }.toInt()
            } catch (e: Exception) {
                0
            }
        } else {
            0
        }
        return i
    }



    /**
     * String转Double
     * @param str
     * @return
     */
    fun stringToDouble(str: String?): Double {
        var i = 0.0
        i = if (str != null) {
            try {
                str.trim { it <= ' ' }.toDouble()
            } catch (e: Exception) {
                0.0
            }
        } else {
            0.0
        }
        return i
    }



}