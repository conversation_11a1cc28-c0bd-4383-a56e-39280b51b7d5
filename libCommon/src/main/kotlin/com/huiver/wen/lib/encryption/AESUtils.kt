package com.huiver.wen.lib.encryption

import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec


/**
 * 加密工具类
 * 主要功能:AES对称加密（Advanced Encryption Standard，高级数据加密标准，AES算法可以有效抵制针对DES的攻击算法，对称加密算法）
 */
class AESUtils {
    private var skeySpec: SecretKeySpec? = null
    private var cipher: Cipher? = null

    constructor(keyraw: ByteArray?) {
        if (keyraw == null) {
            val bytesOfMessage = "".toByteArray(charset("UTF-8"))
            val md = MessageDigest.getInstance("MD5")
            val bytes = md.digest(bytesOfMessage)
            skeySpec = SecretKeySpec(bytes, "AES")
            cipher = Cipher.getInstance("AES/ECB/PKCS5Padding")
        } else {
            skeySpec = SecretKeySpec(keyraw, "AES")
            cipher = Cipher.getInstance("AES/ECB/PKCS5Padding")
        }
    }

    constructor(passphrase: String) {
        val bytesOfMessage = passphrase.toByteArray(charset("UTF-8"))
        val md = MessageDigest.getInstance("MD5")
        val thedigest = md.digest(bytesOfMessage)
        skeySpec = SecretKeySpec(thedigest, "AES")
        cipher = Cipher.getInstance("AES/ECB/PKCS5Padding")
    }

    constructor() {
        val bytesOfMessage = "".toByteArray(charset("UTF-8"))
        val md = MessageDigest.getInstance("MD5")
        val thedigest = md.digest(bytesOfMessage)
        skeySpec = SecretKeySpec(thedigest, "AES")
        skeySpec = SecretKeySpec(ByteArray(16), "AES")
        cipher = Cipher.getInstance("AES/ECB/PKCS5Padding")
    }

    /**
     * 加密:先AES再base64
     *
     * @param plaintext
     * @return
     * @throws Exception
     */
//    @Throws(Exception::class)
//    fun encrypt(plaintext: String): String {
//        cipher!!.init(Cipher.ENCRYPT_MODE, skeySpec)
//        val ciphertext = cipher!!.doFinal(plaintext.toByteArray(charset("UTF-8")))
//        return Base64.encodeToString(ciphertext, Base64.DEFAULT)
//    }
//
//    /**
//     * 解密:先base64再AES
//     *
//     * @param ciphertext
//     * @return
//     * @throws Exception
//     */
//    @Throws(Exception::class)
//    fun decrypt(ciphertext: String?): String {
//        cipher!!.init(Cipher.DECRYPT_MODE, skeySpec)
//        val plain64text = Base64.decode(ciphertext, Base64.DEFAULT)
//        val plaintext = cipher!!.doFinal(plain64text)
//        return String(plaintext, "UTF-8")
//    }

    companion object {
        /*
     * 生成密钥
     */
        @Throws(NoSuchAlgorithmException::class)
        fun initKey(): ByteArray {
            val keyGen = KeyGenerator.getInstance("AES")
            keyGen.init(256) //192 256
            val secretKey = keyGen.generateKey()
            return secretKey.encoded
        }

        /*
     * AES 加密
     */
        @Throws(Exception::class)
        fun encrypt(data: ByteArray?, key: ByteArray?): ByteArray {
            val secretKey: SecretKey = SecretKeySpec(key, "AES")
            val cipher = Cipher.getInstance("AES")
            cipher.init(Cipher.ENCRYPT_MODE, secretKey)
            return cipher.doFinal(data)
        }

        /*
     * AES 解密
     */
        @Throws(Exception::class)
        fun decrypt(data: ByteArray?, key: ByteArray?): ByteArray {
            val secretKey: SecretKey = SecretKeySpec(key, "AES")
            val cipher = Cipher.getInstance("AES")
            cipher.init(Cipher.DECRYPT_MODE, secretKey)
            return cipher.doFinal(data)
        }
    }
}