plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
}
val versionConfig = VersionConfig()
android {
    namespace = "com.huiver.wen.lib.common"
    compileSdk = versionConfig.compileSdk

    defaultConfig {
        minSdk = versionConfig.minSdk

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }


    compileOptions {

        sourceCompatibility = versionConfig.javaSDK
        targetCompatibility =  versionConfig.javaSDK
    }
    kotlinOptions {
        jvmTarget =  versionConfig.javaSDK.toString()
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)

    implementation(libs.navigation.ui.kt)
    implementation(libs.navigation.features)
    implementation(libs.navigation.fragment)

    implementation(libs.timber)
    api(libs.bulgy)


    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)


}