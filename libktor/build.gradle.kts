plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
}
val config = VersionConfig()
android {
    namespace = "com.huiver.wen.ktor"
    compileSdk = config.compileSdk

    defaultConfig {
        minSdk = config.minSdk


        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = config.javaSDK
        targetCompatibility =  config.javaSDK
    }
    kotlinOptions {
        jvmTarget = config.javaSDK.toString()
    }
}

dependencies {

//    implementation(libs.androidx.core.ktx)
//    implementation(libs.androidx.appcompat)
//    implementation(libs.material)


    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}