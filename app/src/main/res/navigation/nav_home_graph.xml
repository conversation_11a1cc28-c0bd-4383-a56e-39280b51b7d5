<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_home_graph"
    app:startDestination="@id/homeFragment">

    <fragment


        tools:layout="@layout/fragment_home"
        android:id="@+id/homeFragment"
        android:name="com.huiver.wen.ui.home.fragment.HomeFragment"
        android:label="Home" />

    <fragment
        tools:layout="@layout/fragment_category"
        android:id="@+id/categoryFragment"
        android:name="com.huiver.wen.ui.home.fragment.CategoryFragment"
        android:label="Category" />

    <fragment
        tools:layout="@layout/fragment_profile"
        android:id="@+id/profileFragment"
        android:name="com.huiver.wen.ui.home.fragment.ProfileFragment"
        android:label="Profile" />
</navigation>
