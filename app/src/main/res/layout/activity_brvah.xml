<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_height="match_parent">
    <androidx.fragment.app.FragmentContainerView
        android:layout_width="match_parent"
        android:id="@+id/nav_host_brvah"
        android:name="androidx.navigation.fragment.NavHostFragment"
        app:defaultNavHost="true"
        app:navGraph="@navigation/nav_brvah_graph"
        android:layout_height="match_parent"/>
</LinearLayout>