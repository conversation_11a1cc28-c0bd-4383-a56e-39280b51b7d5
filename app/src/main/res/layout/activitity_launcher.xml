<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"

    android:orientation="vertical"
    android:layout_height="match_parent">


    <!-- NavHostFragment 承载 Fragment -->
    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/nav_host_fragment"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"

        app:defaultNavHost="true"

        app:navGraph="@navigation/nav_home_graph" />

    <!-- BottomNavigationView 作为底部导航栏 -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"

        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        app:labelVisibilityMode="labeled"
        app:itemIconSize="24dp"
        app:menu="@menu/bottom_nav_menu" />
</LinearLayout>
