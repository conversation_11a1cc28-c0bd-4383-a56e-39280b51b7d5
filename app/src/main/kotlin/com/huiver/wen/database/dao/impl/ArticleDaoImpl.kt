package com.huiver.wen.database.dao.impl

import com.huiver.wen.base.database.dao.PagingDao
import com.huiver.wen.database.entity.Article

/**
 * Created by Jxh on 2025/6/19.
 * description:
 **/
class ArticleDaoImpl(private val dao: com.huiver.wen.database.dao.ArticleDao) : PagingDao<Article> {
    override suspend fun insertAll(items: List<Article>) = dao.insertAll(items)
    override suspend fun get(offset: Int, limit: Int): List<Article> = dao.getArticles(offset, limit)
}
