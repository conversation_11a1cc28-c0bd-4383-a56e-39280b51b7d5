package com.huiver.wen.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.huiver.wen.database.entity.Article

/**
 * Created by Jxh on 2025/6/19.
 * description:
 **/
@Dao
interface ArticleDao {
    @Query("SELECT * FROM article LIMIT :limit OFFSET :offset")
    suspend fun getArticles(offset: Int, limit: Int): List<com.huiver.wen.database.entity.Article>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(items: List<com.huiver.wen.database.entity.Article>)
}