package com.huiver.wen.ui.home.fragment

import android.view.LayoutInflater
import com.huiver.wen.base.ui.fragment.BaseFragment
import com.huiver.wen.databinding.FragmentProfileBinding

class ProfileFragment:BaseFragment<FragmentProfileBinding>() {
    override fun getViewBinding(inflater: LayoutInflater): FragmentProfileBinding {
        return FragmentProfileBinding.inflate(inflater)
    }

    override fun initData() {

    }

    override fun initView() {

    }

    override fun onObserver() {

    }

    override fun onRequest() {

    }
}