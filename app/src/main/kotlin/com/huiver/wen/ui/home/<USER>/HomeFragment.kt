package com.huiver.wen.ui.home.fragment

import android.content.Intent
import android.view.LayoutInflater
import com.huiver.wen.base.ui.fragment.BaseFragment
import com.huiver.wen.databinding.FragmentHomeBinding
import com.huiver.wen.ui.brvah.BRVAHDemoActivity

class HomeFragment:BaseFragment<FragmentHomeBinding>() {
    override fun getViewBinding(inflater: LayoutInflater): FragmentHomeBinding {
        return FragmentHomeBinding.inflate(inflater)
    }

    override fun initData() {

    }

    override fun initView() {

        mBinding.homeBtn.setOnClickListener {
            startActivity(Intent(requireContext(),BRVAHDemoActivity::class.java))

        }
    }

    override fun onObserver() {

    }

    override fun onRequest() {

    }
}