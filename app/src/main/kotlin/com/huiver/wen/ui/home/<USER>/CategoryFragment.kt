package com.huiver.wen.ui.home.fragment

import android.view.LayoutInflater
import com.huiver.wen.base.ui.fragment.BaseFragment
import com.huiver.wen.databinding.FragmentCategoryBinding

class CategoryFragment :BaseFragment<FragmentCategoryBinding>(){
    override fun getViewBinding(inflater: LayoutInflater): FragmentCategoryBinding {
       return FragmentCategoryBinding.inflate(inflater)
    }

    override fun initData() {

    }

    override fun initView() {

    }

    override fun onObserver() {

    }

    override fun onRequest() {

    }
}