package com.huiver.wen.ui.brvah

import android.view.LayoutInflater
import com.huiver.wen.base.ui.activity.BaseActivity
import com.huiver.wen.databinding.ActivityBrvahBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BRVAHDemoActivity :BaseActivity<ActivityBrvahBinding>() {


    override fun getViewBinding(inflater: LayoutInflater): ActivityBrvahBinding {
        return ActivityBrvahBinding.inflate(inflater)
    }

    override fun initData() {

    }

    override fun initView() {

    }

    override fun onObserver() {

    }

    override fun onRequest() {

    }
}