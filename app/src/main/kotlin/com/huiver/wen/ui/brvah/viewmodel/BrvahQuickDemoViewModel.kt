package com.huiver.wen.ui.brvah.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.huiver.wen.entities.BravhItem
import com.huiver.wen.viewmodel.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class BrvahQuickDemoViewModel @Inject constructor() : BaseViewModel() {
    private val _mDemoLiveData = MutableLiveData<List<BravhItem>>()
    val mDemoLiveData:LiveData<List<BravhItem>> = _mDemoLiveData


    fun loadData(){
        viewModelScope.launch(provider.dispatcherIO) {
            val dataList = mutableListOf<BravhItem>()
            dataList.add(BravhItem("Java"))
            dataList.add(BravhItem("C++"))
            dataList.add(BravhItem("Kotlin"))
            dataList.add(BravhItem("Swift"))
            dataList.add(BravhItem("Object C"))

            _mDemoLiveData.postValue(dataList)

        }

    }



    fun loadData2(){
        viewModelScope.launch(provider.dispatcherIO) {
            val dataList = mutableListOf<BravhItem>()
            dataList.add(BravhItem("Java1"))
            dataList.add(BravhItem("C++1"))
            dataList.add(BravhItem("Kotlin1"))
            dataList.add(BravhItem("Swift1"))
            dataList.add(BravhItem("Object C1"))

            _mDemoLiveData.postValue(dataList)

        }

    }

}