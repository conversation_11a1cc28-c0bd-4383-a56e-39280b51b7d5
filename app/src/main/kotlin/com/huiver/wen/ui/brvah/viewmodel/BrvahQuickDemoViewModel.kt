package com.huiver.wen.ui.brvah.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.huiver.wen.base.common.data.PageSizeData
import com.huiver.wen.base.net.http.BaseResponse
import com.huiver.wen.base.ui.widget.ViewBindingHolder
import com.huiver.wen.databinding.ItemBrvahBinding
import com.huiver.wen.entities.BravhItem
import com.huiver.wen.ui.brvah.adapter.DemoQuickAdapter
import com.huiver.wen.viewmodel.BasePageViewModel
import kotlinx.coroutines.launch

class BrvahQuickDemoViewModel(adapter: DemoQuickAdapter):BasePageViewModel<BravhItem, ViewBindingHolder<ItemBrvahBinding>>(adapter) {
    private val _mDemoLiveData = MutableLiveData<List<BravhItem>>()
    val mDemoLiveData:LiveData<List<BravhItem>> = _mDemoLiveData


    fun loadData(){
        viewModelScope.launch(provider.dispatcherIO) {
            val dataList = mutableListOf<BravhItem>()
            dataList.add(BravhItem("Java"))
            dataList.add(BravhItem("C++"))
            dataList.add(BravhItem("Kotlin"))
            dataList.add(BravhItem("Swift"))
            dataList.add(BravhItem("Object C"))

            _mDemoLiveData.postValue(dataList)

        }

    }

    override suspend fun onRequestNet(pageData: PageSizeData): BaseResponse<List<BravhItem>> {
        val dataList = mutableListOf<BravhItem>()
        dataList.add(BravhItem("Java1"))
        dataList.add(BravhItem("C++1"))
        dataList.add(BravhItem("Kotlin1"))
        dataList.add(BravhItem("Swift1"))
        dataList.add(BravhItem("Object C1"))
        return BaseResponse(data = dataList)
    }

    fun loadData2(){
        viewModelScope.launch(provider.dispatcherIO) {
            val dataList = mutableListOf<BravhItem>()
            dataList.add(BravhItem("Java1"))
            dataList.add(BravhItem("C++1"))
            dataList.add(BravhItem("Kotlin1"))
            dataList.add(BravhItem("Swift1"))
            dataList.add(BravhItem("Object C1"))

            _mDemoLiveData.postValue(dataList)

        }

    }
    
    class Factory(private val adapter: DemoQuickAdapter) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(BrvahQuickDemoViewModel::class.java)) {
                @Suppress("UNCHECKED_CAST")
                return BrvahQuickDemoViewModel(adapter) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
}