package com.huiver.wen.ui.brvah.fragment

import android.view.LayoutInflater
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.huiver.wen.base.ui.fragment.BaseFragment
import com.huiver.wen.databinding.FragmentListBinding
import com.huiver.wen.ui.brvah.adapter.DemoQuickAdapter
import com.huiver.wen.ui.brvah.viewmodel.BrvahQuickDemoViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject


class QuickAdapterFragment :BaseFragment<FragmentListBinding>(){

    private val demoQuickAdapter by lazy { DemoQuickAdapter() }
    
    private lateinit var mViewModel: BrvahQuickDemoViewModel
    
    override fun getViewBinding(inflater: LayoutInflater): FragmentListBinding {

        return FragmentListBinding.inflate(inflater)
    }

    override fun initData() {
        // 在initData中创建ViewModel，确保依赖注入已完成
        mViewModel = BrvahQuickDemoViewModel.Factory(demoQuickAdapter).create(BrvahQuickDemoViewModel::class.java)
    }

    override fun initView() {
        mBinding.recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = demoQuickAdapter
        }
    }

    override fun onObserver() {
        mViewModel.mDemoLiveData.observe(this){
            demoQuickAdapter.addAll(it)
        }
    }

    override fun onRequest() {
        mViewModel.loadData()
        lifecycleScope.launch (Dispatchers.IO){
            delay(2000)
            mViewModel.loadData2()
        }
    }
}