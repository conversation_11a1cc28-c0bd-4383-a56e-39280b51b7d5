package com.huiver.wen.ui.brvah.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.huiver.wen.base.ui.fragment.BaseFragment
import com.huiver.wen.databinding.FragmentListBinding
import com.huiver.wen.ui.brvah.adapter.DemoQuickAdapter
import com.huiver.wen.ui.brvah.viewmodel.BrvahQuickDemoViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject


@AndroidEntryPoint
class QuickAdapterFragment :BaseFragment<FragmentListBinding>(){

    @Inject
    lateinit var demoQuickAdapter: DemoQuickAdapter

    private val mViewModel: BrvahQuickDemoViewModel by viewModels()
    
    override fun getViewBinding(inflater: LayoutInflater): FragmentListBinding {
        return FragmentListBinding.inflate(inflater)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // 在 onViewCreated 中设置 adapter，确保依赖注入已完成
        mBinding.recyclerView.adapter = demoQuickAdapter
    }

    override fun initData() {
        // ViewModel 和 Adapter 现在通过 Hilt 自动注入，无需手动创建
    }

    override fun initView() {
        mBinding.recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            // adapter 将在 onViewCreated 中设置，确保依赖注入完成
        }
    }

    override fun onObserver() {
        mViewModel.mDemoLiveData.observe(this){
            demoQuickAdapter.addAll(it)
        }
    }

    override fun onRequest() {
        mViewModel.loadData()
        lifecycleScope.launch (Dispatchers.IO){
            delay(2000)
            mViewModel.loadData2()
        }
    }
}