package com.huiver.wen.ui.home

import android.graphics.Color
import android.view.LayoutInflater
import androidx.navigation.ui.setupWithNavController
import com.huiver.wen.extension.findNavController
import com.google.android.material.badge.BadgeDrawable
import com.huiver.wen.R
import com.huiver.wen.base.ui.activity.BaseActivity
import com.huiver.wen.databinding.ActivitityLauncherBinding



class LauncherActivity :BaseActivity<ActivitityLauncherBinding>() {

    override fun getViewBinding(inflater: LayoutInflater): ActivitityLauncherBinding {
       return ActivitityLauncherBinding.inflate(inflater)
    }

    override fun initData() {

    }

    override fun initView() {
        val navController = findNavController(R.id.nav_host_fragment)

        mBinding.bottomNavigation.setupWithNavController(navController)

        val badge =  mBinding.bottomNavigation.getOrCreateBadge(R.id.homeFragment)
        badge.isVisible = true
        badge.backgroundColor = Color.RED // 自定义颜色
        badge.badgeGravity = BadgeDrawable.TOP_END
        badge.maxCharacterCount = 2  // 最多几位数字（可选）

    }

    override fun onObserver() {

    }

    override fun onRequest() {

    }
}