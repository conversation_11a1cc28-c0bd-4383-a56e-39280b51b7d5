import com.android.ide.common.repository.pickPluginVariableName
import com.android.sdklib.AndroidVersion.VersionCodes

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
}


val versionConfig = VersionConfig()


android {
    namespace = "com.huiver.wen"
    compileSdk = versionConfig.compileSdk

    defaultConfig {
        applicationId = "com.huiver.wen"
        minSdk = versionConfig.minSdk
        targetSdk = versionConfig.targetSdk
        versionCode = versionConfig.versionCode
        versionName =  versionConfig.versionName

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            // 启用代码混淆
            isMinifyEnabled = true

            // 启用资源压缩（可选）
            isShrinkResources  = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    buildFeatures {
        buildConfig = true
        viewBinding = true
    }

    compileOptions {

        sourceCompatibility = versionConfig.javaSDK
        targetCompatibility =  versionConfig.javaSDK
    }
    kotlinOptions {
        jvmTarget =  versionConfig.javaSDK.toString()
    }
}

dependencies {

    api(project(":baseModule"))
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)



    implementation(libs.refresh.layout.kernel)
    implementation(libs.refresh.header.classics)
    implementation(libs.refresh.footer.classics)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}